<?php

namespace Space\MongoDocuments\Repository;

use Doctrine\ODM\MongoDB\Repository\DocumentRepository;
use Space\MongoDocuments\Document\VehicleLabel;

class VehicleLabelRepository extends DocumentRepository
{
    /**
     * Find vehicle label by LCDV code with longest matching strategy
     * This method implements the same logic as the original MongoDB aggregation
     */
    public function findByLcdvWithLongestMatch(string $lcdvCode): ?VehicleLabel
    {
        // Get all possible LCDV formats (this should match LcdvsProvider::getLcdvsFormatArray)
        $lcdvFormats = $this->getLcdvsFormatArray($lcdvCode);
        
        // Find all matching documents
        $qb = $this->createQueryBuilder()
            ->field('lcdv')->in($lcdvFormats);
            
        $results = $qb->getQuery()->execute()->toArray();
        
        if (empty($results)) {
            return null;
        }
        
        // Sort by longest matching LCDV length (mimicking the original aggregation logic)
        usort($results, function($a, $b) {
            $aMaxLength = $this->getMaxLcdvLength($a->getLcdv());
            $bMaxLength = $this->getMaxLcdvLength($b->getLcdv());
            return $bMaxLength <=> $aMaxLength; // Descending order
        });
        
        return $results[0] ?? null;
    }
    
    /**
     * Find vehicle label by exact LCDV match
     */
    public function findByLcdv(string $lcdv): ?VehicleLabel
    {
        return $this->findOneBy(['lcdv' => $lcdv]);
    }
    
    /**
     * Find vehicle labels by multiple LCDV codes
     */
    public function findByLcdvCodes(array $lcdvCodes): array
    {
        return $this->findBy(['lcdv' => ['$in' => $lcdvCodes]]);
    }
    
    /**
     * Get LCDV format array (using the actual LcdvsProvider logic)
     */
    private function getLcdvsFormatArray(string $lcdvCode): array
    {
        // Use the same logic as LcdvsProvider::getLcdvsFormatArray
        $lcdvs = [];
        $list = range(2, strlen($lcdvCode)) ?? [];
        foreach ($list as $length) {
            $lcdvs[] = substr($lcdvCode, 0, $length);
        }
        return $lcdvs;
    }
    
    /**
     * Get maximum LCDV length from array
     */
    private function getMaxLcdvLength(array $lcdvArray): int
    {
        if (empty($lcdvArray)) {
            return 0;
        }
        
        $maxLength = 0;
        foreach ($lcdvArray as $lcdv) {
            $length = is_string($lcdv) ? strlen($lcdv) : 0;
            if ($length > $maxLength) {
                $maxLength = $length;
            }
        }
        
        return $maxLength;
    }
}

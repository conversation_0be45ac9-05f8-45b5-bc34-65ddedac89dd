<?php

namespace Space\MongoDocuments\Repository;

use Doctrine\ODM\MongoDB\Repository\DocumentRepository;
use Space\MongoDocuments\Document\SPSEligibility;

class SPSEligibilityRepository extends DocumentRepository
{
    /**
     * Get SPS eligibility data grouped by scope (LCDV and MODEL)
     * This method replicates the original MongoDB Atlas aggregation with $facet
     * 
     * @return array Returns array with 'lcdv' and 'model' keys containing respective eligibility rules
     */
    public function getSPSEligibilityData(): array
    {
        // Get LCDV scope eligibility rules
        $lcdvEligibility = $this->findBy(['scope' => 'LCDV']);
        
        // Get MODEL scope eligibility rules  
        $modelEligibility = $this->findBy(['scope' => 'MODEL']);
        
        // Convert to arrays for backward compatibility
        $lcdvArray = [];
        foreach ($lcdvEligibility as $eligibility) {
            $lcdvArray[] = $eligibility->toArray();
        }
        
        $modelArray = [];
        foreach ($modelEligibility as $eligibility) {
            $modelArray[] = $eligibility->toArray();
        }
        
        return [
            'lcdv' => $lcdvArray,
            'model' => $modelArray,
        ];
    }

    /**
     * Find eligibility rules by scope
     */
    public function findByScope(string $scope): array
    {
        return $this->findBy(['scope' => $scope]);
    }

    /**
     * Find eligibility rule by LCDV code (first 4 characters)
     */
    public function findByLCDVCode(string $lcdvCode): ?SPSEligibility
    {
        $qb = $this->createQueryBuilder();
        
        $qb->field('scope')->equals('LCDV')
           ->field('codes')->in([$lcdvCode]);
           
        return $qb->getQuery()->getSingleResult();
    }

    /**
     * Find all active eligibility rules
     */
    public function findActive(): array
    {
        return $this->findBy(['active' => true]);
    }

    /**
     * Find eligibility rules by type
     */
    public function findByType(string $type): array
    {
        return $this->findBy(['type' => $type]);
    }

    /**
     * Check if a specific LCDV code has eligibility rules
     */
    public function hasEligibilityForLCDV(string $lcdvCode): bool
    {
        $qb = $this->createQueryBuilder();
        
        $qb->field('scope')->equals('LCDV')
           ->field('codes')->in([$lcdvCode])
           ->count();
           
        return $qb->getQuery()->execute() > 0;
    }

    /**
     * Get eligibility statistics
     */
    public function getEligibilityStats(): array
    {
        $total = $this->createQueryBuilder()->count()->getQuery()->execute();
        $lcdvCount = $this->createQueryBuilder()->field('scope')->equals('LCDV')->count()->getQuery()->execute();
        $modelCount = $this->createQueryBuilder()->field('scope')->equals('MODEL')->count()->getQuery()->execute();
        $activeCount = $this->createQueryBuilder()->field('active')->equals(true)->count()->getQuery()->execute();
        
        return [
            'total' => $total,
            'lcdv' => $lcdvCount,
            'model' => $modelCount,
            'active' => $activeCount,
            'inactive' => $total - $activeCount,
        ];
    }
}

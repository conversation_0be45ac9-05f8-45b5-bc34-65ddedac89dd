<?php

namespace Space\MongoDocuments\Document;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

#[MongoDB\EmbeddedDocument]
class Vehicle
{
    // Fields ordered to match MongoDB document structure

    // MongoDB document ID field (maps to 'id' in MongoDB)
    #[MongoDB\Field(type: 'string', name: 'id')]
    private ?string $documentId = null;

    // Primary identifier - VIN always first
    #[MongoDB\Field(type: 'string')]
    private ?string $vin = null;

    // Label field (maps to 'label' in MongoDB, used as model description)
    #[MongoDB\Field(type: 'string', name: 'label')]
    private ?string $label = null;

    // Core vehicle identification
    #[MongoDB\Field(type: 'string')]
    private ?string $brand = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $model = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $version = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $versionId = null;

    // Registration information
    #[MongoDB\Field(type: 'string')]
    private ?string $registrationNumber = null;

    #[MongoDB\Field(type: 'date')]
    private ?\DateTime $registrationDate = null;

    // User customization fields
    #[MongoDB\Field(type: 'string')]
    private ?string $nickName = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $picture = null;

    // Vehicle characteristics
    #[MongoDB\Field(type: 'string')]
    private ?string $color = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $energy = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $type = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $sdp = null;

    // Additional vehicle information fields (matching legacy Vehicle document)
    #[MongoDB\Field(type: 'string')]
    private ?string $shortLabel = null;

    #[MongoDB\Field(type: 'int')]
    private ?int $lastUpdate = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $year = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $country = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $market = null;

    #[MongoDB\Field(type: 'int')]
    private ?int $regTimeStamp = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $warrantyStartDate = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $make = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $subMake = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $enrollmentStatus = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $connectorType = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $addStatus = null;

    // Feature codes (backward compatibility array) - matches MongoDB order
    #[MongoDB\Field(type: 'collection')]
    private array $featureCode = [];

    // Preferred dealer information
    #[MongoDB\Field(type: 'hash')]
    private ?array $preferredDealer = null;

    // Order-related fields for vehicle order management
    #[MongoDB\Field(type: 'bool')]
    private ?bool $isOrder = null;

    #[MongoDB\EmbedOne(targetDocument: VehicleOrder::class)]
    private ?VehicleOrder $vehicleOrder = null;

    // Enhanced embedded documents
    #[MongoDB\EmbedMany(targetDocument: FeatureCode::class)]
    private Collection $featureCodes;

    #[MongoDB\EmbedOne(targetDocument: MileageData::class)]
    private ?MileageData $mileage = null;

    public function __construct()
    {
        $this->featureCodes = new ArrayCollection();
    }

    // Document ID methods
    public function getDocumentId(): ?string
    {
        return $this->documentId;
    }

    public function setDocumentId(?string $documentId): self
    {
        $this->documentId = $documentId;
        return $this;
    }

    // Primary identifier methods - VIN first
    public function getVin(): ?string
    {
        return $this->vin;
    }

    public function setVin(?string $vin): self
    {
        $this->vin = $vin;
        return $this;
    }

    // Label methods (maps to MongoDB 'label' field)
    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(?string $label): self
    {
        $this->label = $label;
        return $this;
    }

    // Core vehicle identification methods (matching MongoDB order)
    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function setBrand(?string $brand): self
    {
        $this->brand = $brand;
        return $this;
    }

    public function getModel(): ?string
    {
        return $this->model;
    }

    public function setModel(?string $model): self
    {
        $this->model = $model;
        return $this;
    }

    public function getVersion(): ?string
    {
        return $this->version;
    }

    public function setVersion(?string $version): self
    {
        $this->version = $version;
        return $this;
    }

    public function getVersionId(): ?string
    {
        return $this->versionId;
    }

    public function setVersionId(?string $versionId): self
    {
        $this->versionId = $versionId;
        return $this;
    }

    // Registration information methods
    public function getRegistrationNumber(): ?string
    {
        return $this->registrationNumber;
    }

    public function setRegistrationNumber(?string $registrationNumber): self
    {
        $this->registrationNumber = $registrationNumber;
        return $this;
    }

    public function getRegistrationDate(): ?\DateTime
    {
        return $this->registrationDate;
    }

    public function setRegistrationDate(?\DateTime $registrationDate): self
    {
        $this->registrationDate = $registrationDate;
        return $this;
    }

    // User customization methods
    public function getNickName(): ?string
    {
        return $this->nickName;
    }

    public function setNickName(?string $nickName): self
    {
        $this->nickName = $nickName;
        return $this;
    }

    public function getPicture(): ?string
    {
        return $this->picture;
    }

    public function setPicture(?string $picture): self
    {
        $this->picture = $picture;
        return $this;
    }

    // Vehicle characteristics methods
    public function getColor(): ?string
    {
        return $this->color;
    }

    public function setColor(?string $color): self
    {
        $this->color = $color;
        return $this;
    }

    public function getEnergy(): ?string
    {
        return $this->energy;
    }

    public function setEnergy(?string $energy): self
    {
        $this->energy = $energy;
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->sdp;
    }

    public function setStatus(?string $sdp): self
    {
        $this->sdp = $sdp;
        return $this;
    }

    // Additional vehicle information methods
    public function getShortLabel(): ?string
    {
        return $this->shortLabel;
    }

    public function setShortLabel(?string $shortLabel): self
    {
        $this->shortLabel = $shortLabel;
        return $this;
    }

    public function getLastUpdate(): ?int
    {
        return $this->lastUpdate;
    }

    public function setLastUpdate(?int $lastUpdate): self
    {
        $this->lastUpdate = $lastUpdate;
        return $this;
    }

    public function getYear(): ?string
    {
        return $this->year;
    }

    public function setYear(?string $year): self
    {
        $this->year = $year;
        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): self
    {
        $this->country = $country;
        return $this;
    }

    public function getMarket(): ?string
    {
        return $this->market;
    }

    public function setMarket(?string $market): self
    {
        $this->market = $market;
        return $this;
    }

    public function getRegTimeStamp(): ?int
    {
        return $this->regTimeStamp;
    }

    public function setRegTimeStamp(?int $regTimeStamp): self
    {
        $this->regTimeStamp = $regTimeStamp;
        return $this;
    }

    public function getWarrantyStartDate(): ?string
    {
        return $this->warrantyStartDate;
    }

    public function setWarrantyStartDate(?string $warrantyStartDate): self
    {
        $this->warrantyStartDate = $warrantyStartDate;
        return $this;
    }

    public function getMake(): ?string
    {
        return $this->make;
    }

    public function setMake(?string $make): self
    {
        $this->make = $make;
        return $this;
    }

    public function getSubMake(): ?string
    {
        return $this->subMake;
    }

    public function setSubMake(?string $subMake): self
    {
        $this->subMake = $subMake;
        return $this;
    }

    public function getEnrollmentStatus(): ?string
    {
        return $this->enrollmentStatus;
    }

    public function setEnrollmentStatus(?string $enrollmentStatus): self
    {
        $this->enrollmentStatus = $enrollmentStatus;
        return $this;
    }

    public function getConnectorType(): ?string
    {
        return $this->connectorType;
    }

    public function setConnectorType(?string $connectorType): self
    {
        $this->connectorType = $connectorType;
        return $this;
    }

    public function getAddStatus(): ?string
    {
        return $this->addStatus;
    }

    public function setAddStatus(?string $addStatus): self
    {
        $this->addStatus = $addStatus;
        return $this;
    }

    public function getFeatureCode(): array
    {
        return $this->featureCode;
    }

    public function setFeatureCode(array $featureCode): self
    {
        $this->featureCode = $featureCode;
        return $this;
    }

    // Preferred dealer methods
    public function getPreferredDealer(): ?array
    {
        return $this->preferredDealer;
    }

    public function setPreferredDealer(?array $preferredDealer): self
    {
        $this->preferredDealer = $preferredDealer;
        return $this;
    }

    /**
     * Find a specific feature code by code name
     */
    public function findFeatureCode(string $code): ?array
    {
        foreach ($this->featureCode as $featureCodeItem) {
            if (isset($featureCodeItem['code']) && $featureCodeItem['code'] === $code) {
                return $featureCodeItem;
            }
        }
        return null;
    }

    /**
     * Add a feature code to the vehicle
     */
    public function addFeatureCode(array $featureCodeItem): self
    {
        $this->featureCode[] = $featureCodeItem;
        return $this;
    }

    // Enhanced FeatureCode methods using embedded documents

    /**
     * Get feature codes as FeatureCode objects
     */
    public function getFeatureCodes(): Collection
    {
        return $this->featureCodes;
    }

    /**
     * Set feature codes from FeatureCode objects
     */
    public function setFeatureCodes(Collection $featureCodes): self
    {
        $this->featureCodes = $featureCodes;
        return $this;
    }

    /**
     * Add a FeatureCode object
     */
    public function addFeatureCodeObject(FeatureCode $featureCode): self
    {
        $this->featureCodes->add($featureCode);
        return $this;
    }

    /**
     * Remove a FeatureCode object
     */
    public function removeFeatureCodeObject(FeatureCode $featureCode): self
    {
        $this->featureCodes->removeElement($featureCode);
        return $this;
    }

    /**
     * Find a specific feature code by code name (enhanced version)
     */
    public function findFeatureCodeObject(string $code): ?FeatureCode
    {
        foreach ($this->featureCodes as $featureCode) {
            if ($featureCode->getCode() === $code) {
                return $featureCode;
            }
        }
        return null;
    }

    /**
     * Check if a feature code is enabled
     */
    public function isFeatureCodeEnabled(string $code): bool
    {
        $featureCode = $this->findFeatureCodeObject($code);
        return $featureCode && $featureCode->isEnabled();
    }

    /**
     * Get all enabled feature codes
     */
    public function getEnabledFeatureCodes(): array
    {
        $enabled = [];
        foreach ($this->featureCodes as $featureCode) {
            if ($featureCode->isEnabled()) {
                $enabled[] = $featureCode;
            }
        }
        return $enabled;
    }

    // Mileage methods

    /**
     * Get mileage data
     */
    public function getMileage(): ?MileageData
    {
        return $this->mileage;
    }

    /**
     * Set mileage data
     */
    public function setMileage(?MileageData $mileage): self
    {
        $this->mileage = $mileage;
        return $this;
    }

    /**
     * Set mileage from array (backward compatibility)
     */
    public function setMileageFromArray(array $mileageData): self
    {
        $this->mileage = MileageData::fromArray($mileageData);
        return $this;
    }

    /**
     * Get mileage as array (backward compatibility)
     */
    public function getMileageAsArray(): ?array
    {
        return $this->mileage?->toArray();
    }

    // Backward compatibility and synchronization methods

    /**
     * Sync feature codes from array to embedded documents
     */
    public function syncFeatureCodesFromArray(): self
    {
        $this->featureCodes->clear();

        foreach ($this->featureCode as $featureCodeData) {
            if (is_array($featureCodeData)) {
                $this->featureCodes->add(FeatureCode::fromArray($featureCodeData));
            }
        }

        return $this;
    }

    /**
     * Sync feature codes from embedded documents to array
     */
    public function syncFeatureCodesToArray(): self
    {
        $this->featureCode = [];

        foreach ($this->featureCodes as $featureCode) {
            $this->featureCode[] = $featureCode->toArray();
        }

        return $this;
    }

    /**
     * Set feature codes from array and sync to embedded documents
     */
    public function setFeatureCodeWithSync(array $featureCode): self
    {
        $this->featureCode = $featureCode;
        $this->syncFeatureCodesFromArray();
        return $this;
    }

    /**
     * Get feature codes as array with sync from embedded documents
     */
    public function getFeatureCodeWithSync(): array
    {
        $this->syncFeatureCodesToArray();
        return $this->featureCode;
    }

    /**
     * Auto-sync method to be called before persistence
     */
    public function prePersist(): void
    {
        // Sync embedded documents to arrays for backward compatibility
        $this->syncFeatureCodesToArray();
    }

    /**
     * Auto-sync method to be called after loading from database
     */
    public function postLoad(): void
    {
        // Sync arrays to embedded documents for enhanced functionality
        $this->syncFeatureCodesFromArray();
    }

    // Order-related methods

    public function getIsOrder(): ?bool
    {
        return $this->isOrder;
    }

    public function setIsOrder(?bool $isOrder): self
    {
        $this->isOrder = $isOrder;
        return $this;
    }

    public function getVehicleOrder(): ?VehicleOrder
    {
        return $this->vehicleOrder;
    }

    public function setVehicleOrder(?VehicleOrder $vehicleOrder): self
    {
        $this->vehicleOrder = $vehicleOrder;
        return $this;
    }
}

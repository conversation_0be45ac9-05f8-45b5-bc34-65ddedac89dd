<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use Space\MongoDocuments\Repository\SPSEligibilityRepository;

#[MongoDB\Document(collection: 'boSPSEligibility', repositoryClass: SPSEligibilityRepository::class)]
class SPSEligibility
{
    #[MongoDB\Id]
    private ?string $id = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $scope = null;

    #[MongoDB\Field(type: 'collection')]
    private array $codes = [];

    #[MongoDB\Field(type: 'string')]
    private ?string $eligibilityRule = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $type = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $eligibilityDisclaimer = null;

    // Additional fields that might be present in the collection
    #[MongoDB\Field(type: 'string')]
    private ?string $name = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $description = null;

    #[MongoDB\Field(type: 'bool')]
    private ?bool $active = null;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getScope(): ?string
    {
        return $this->scope;
    }

    public function setScope(?string $scope): self
    {
        $this->scope = $scope;
        return $this;
    }

    public function getCodes(): array
    {
        return $this->codes;
    }

    public function setCodes(array $codes): self
    {
        $this->codes = $codes;
        return $this;
    }

    public function getEligibilityRule(): ?string
    {
        return $this->eligibilityRule;
    }

    public function setEligibilityRule(?string $eligibilityRule): self
    {
        $this->eligibilityRule = $eligibilityRule;
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;
        return $this;
    }

    public function getEligibilityDisclaimer(): ?string
    {
        return $this->eligibilityDisclaimer;
    }

    public function setEligibilityDisclaimer(?string $eligibilityDisclaimer): self
    {
        $this->eligibilityDisclaimer = $eligibilityDisclaimer;
        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;
        return $this;
    }

    public function getActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(?bool $active): self
    {
        $this->active = $active;
        return $this;
    }

    /**
     * Convert to array format for backward compatibility
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'scope' => $this->scope,
            'codes' => $this->codes,
            'eligibilityRule' => $this->eligibilityRule,
            'type' => $this->type,
            'eligibilityDisclaimer' => $this->eligibilityDisclaimer,
            'name' => $this->name,
            'description' => $this->description,
            'active' => $this->active,
        ];
    }
}

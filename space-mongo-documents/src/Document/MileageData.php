<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

#[MongoDB\EmbeddedDocument]
class MileageData
{
    #[MongoDB\Field(type: 'int')]
    private ?int $value = null;

    #[MongoDB\Field(type: 'int')]
    private ?int $date = null;

    #[MongoDB\Field(type: 'int')]
    private ?int $timestamp = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $unit = 'km';

    public function getValue(): ?int
    {
        return $this->value;
    }

    public function setValue(?int $value): self
    {
        $this->value = $value;
        return $this;
    }

    public function getDate(): ?int
    {
        return $this->date;
    }

    public function setDate(?int $date): self
    {
        $this->date = $date;
        return $this;
    }

    /**
     * Get date as DateTime object
     */
    public function getDateAsDateTime(): ?\DateTime
    {
        if (!$this->date) {
            return null;
        }

        return new \DateTime('@' . $this->date);
    }

    public function getTimestamp(): ?int
    {
        return $this->timestamp;
    }

    public function setTimestamp(?int $timestamp): self
    {
        $this->timestamp = $timestamp;

        // Auto-set date from timestamp if provided (date field stores epoch timestamp)
        if ($timestamp !== null) {
            $this->date = $timestamp;
        }

        return $this;
    }

    public function getUnit(): ?string
    {
        return $this->unit;
    }

    public function setUnit(?string $unit): self
    {
        $this->unit = $unit;
        return $this;
    }

    /**
     * Convert to array format for backward compatibility
     */
    public function toArray(): array
    {
        $result = [
            'value' => $this->value,
        ];

        if ($this->date) {
            $result['date'] = $this->date;
        } elseif ($this->timestamp) {
            $result['date'] = $this->timestamp;
        }

        if ($this->unit && $this->unit !== 'km') {
            $result['unit'] = $this->unit;
        }

        return $result;
    }

    /**
     * Create from array data for backward compatibility
     */
    public static function fromArray(array $data): self
    {
        $mileage = new self();
        $mileage->setValue($data['value'] ?? null);
        $mileage->setUnit($data['unit'] ?? 'km');

        // Handle date/timestamp - date field should store epoch timestamp
        if (isset($data['date'])) {
            if (is_int($data['date'])) {
                $mileage->setDate($data['date']);
                $mileage->setTimestamp($data['date']);
            } elseif ($data['date'] instanceof \DateTime) {
                $timestamp = $data['date']->getTimestamp();
                $mileage->setDate($timestamp);
                $mileage->setTimestamp($timestamp);
            }
        }

        return $mileage;
    }

    /**
     * Get mileage in miles (if stored in km)
     */
    public function getValueInMiles(): ?float
    {
        if ($this->value === null) {
            return null;
        }

        if ($this->unit === 'miles') {
            return (float) $this->value;
        }

        // Convert km to miles
        return round($this->value * 0.621371, 2);
    }

    /**
     * Get mileage in kilometers (if stored in miles)
     */
    public function getValueInKm(): ?float
    {
        if ($this->value === null) {
            return null;
        }

        if ($this->unit === 'km') {
            return (float) $this->value;
        }

        // Convert miles to km
        return round($this->value * 1.60934, 2);
    }

    /**
     * Check if mileage data is recent (within last 30 days)
     */
    public function isRecent(): bool
    {
        if (!$this->date) {
            return false;
        }

        $thirtyDaysAgo = time() - (30 * 24 * 60 * 60); // 30 days in seconds
        return $this->date >= $thirtyDaysAgo;
    }

    /**
     * Get formatted date string
     */
    public function getFormattedDate(string $format = 'Y-m-d'): ?string
    {
        if (!$this->date) {
            return null;
        }

        $dateTime = new \DateTime('@' . $this->date);
        return $dateTime->format($format);
    }
}

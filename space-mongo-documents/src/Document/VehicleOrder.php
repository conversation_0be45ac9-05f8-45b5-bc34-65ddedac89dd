<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;

#[MongoDB\EmbeddedDocument]
class VehicleOrder
{
    #[MongoDB\Field(type: 'string')]
    private ?string $mopId = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $orderFormId = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $orderFormStatus = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $trackingStatus = null;

    #[MongoDB\Field(type: 'bool')]
    private ?bool $isUpdated = null;

    // Getters and Setters

    public function getMopId(): ?string
    {
        return $this->mopId;
    }

    public function setMopId(?string $mopId): self
    {
        $this->mopId = $mopId;
        return $this;
    }

    public function getOrderFormId(): ?string
    {
        return $this->orderFormId;
    }

    public function setOrderFormId(?string $orderFormId): self
    {
        $this->orderFormId = $orderFormId;
        return $this;
    }

    public function getOrderFormStatus(): ?string
    {
        return $this->orderFormStatus;
    }

    public function setOrderFormStatus(?string $orderFormStatus): self
    {
        $this->orderFormStatus = $orderFormStatus;
        return $this;
    }

    public function getTrackingStatus(): ?string
    {
        return $this->trackingStatus;
    }

    public function setTrackingStatus(?string $trackingStatus): self
    {
        $this->trackingStatus = $trackingStatus;
        return $this;
    }

    public function getIsUpdated(): ?bool
    {
        return $this->isUpdated;
    }

    public function setIsUpdated(?bool $isUpdated): self
    {
        $this->isUpdated = $isUpdated;
        return $this;
    }

    /**
     * Create VehicleOrder from array data
     */
    public static function fromArray(array $data): self
    {
        $vehicleOrder = new self();
        $vehicleOrder->setMopId($data['mopId'] ?? null);
        $vehicleOrder->setOrderFormId($data['orderFormId'] ?? null);
        $vehicleOrder->setOrderFormStatus($data['orderFormStatus'] ?? null);
        $vehicleOrder->setTrackingStatus($data['trackingStatus'] ?? null);
        $vehicleOrder->setIsUpdated($data['isUpdated'] ?? null);
        
        return $vehicleOrder;
    }

    /**
     * Convert VehicleOrder to array
     */
    public function toArray(): array
    {
        return [
            'mopId' => $this->mopId,
            'orderFormId' => $this->orderFormId,
            'orderFormStatus' => $this->orderFormStatus,
            'trackingStatus' => $this->trackingStatus,
            'isUpdated' => $this->isUpdated,
        ];
    }
}

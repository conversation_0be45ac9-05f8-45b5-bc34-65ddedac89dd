<?php

namespace App\Service;

use App\Connector\GigyaApiConnector;
use App\Connector\GigyaGlobalApiConnector;
use App\Connector\CVSInternalApiConnector;
use App\Connector\NaftaGigyaApiConnector;
use App\Helper\WSResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Trait\LoggerTrait;

/**
 * User service.
 */
class UserService
{
    use LoggerTrait;

    /**
     * @var GigyaApiConnector
     */
    private $connector;

    /**
     * @var string
     */
    private $userKey;

    /**
     * @var string
     */
    private $secretKey;

    /**
     * @var array
     */
    private $options;

    public function __construct(
        GigyaApiConnector $connector,
        private GigyaGlobalApiConnector $connectorGlobal,
        private CVSInternalApiConnector $internalConnector,
        private NaftaGigyaApiConnector $naftaGigyaApiConnector,
        string $userKey,
        string $secretKey,
        private string $globalUserKey,
        private string $globalSecretKey,
        private string $username,
        private string $password,
        private string $clientId,
        private string $certDir,
        private string $certName,
        private string $naftaUserKey,
        private string $naftaSecretKey
    ) {
        $this->connector = $connector;
        $this->userKey = $userKey;
        $this->secretKey = $secretKey;
        $this->options = [
            'verify_peer' => false,
            'auth_basic' => [$this->username, $this->password],
            'local_cert' => $this->getCertifFile($this->certName, '.cer'),
            'local_pk' => $this->getCertifFile($this->certName, '.key'),
            'timeout' => 7.0,
        ];
    }

    /**
     * Get user from Gigya API by CVSID.
     */
    public function getUserByCvs(string $userCvsId, string $brand): WSResponse
    {
        $userCvsId = $this->checkValue($userCvsId);

        if ($brand) {
            if (in_array($brand, ['JE', 'AR'])) {
                $brand = 'AP';
            }
            $query = "SELECT * FROM accounts WHERE data.CVSID.$brand = '$userCvsId'";
            return $this->accountsSearch($query);
        }

        return new WSResponse(Response::HTTP_BAD_REQUEST, 'Bad request');
    }

    /**
     * Get user from Gigya API by userId (UID).
     */
    public function getUserById(string $userId): WSResponse
    {
        if ($userId) {
            $userId = $this->checkValue($userId);
            $query = "SELECT * FROM emailAccounts WHERE UID = '$userId'";

            return $this->accountsSearch($query);
        }

        return new WSResponse(Response::HTTP_BAD_REQUEST, 'Bad request');
    }

    public function getNaftaUserByCvs(string $userCvsId, string $brand): WSResponse
    {
        $userCvsId = $this->checkValue($userCvsId);

        if ($brand) {
            if (in_array($brand, ['JE', 'AR'])) {
                $brand = 'AP';
            }
            $query = "SELECT * FROM accounts WHERE data.CVSID.$brand = '$userCvsId'";
            return $this->naftaAccountsSearch($query);
        }

        return new WSResponse(Response::HTTP_BAD_REQUEST, 'Bad request');
    }

    public function getNaftaUserById(string $userId): WSResponse
    {
        if ($userId) {
            $userId = $this->checkValue($userId);
            $query = "SELECT * FROM emailAccounts WHERE UID = '$userId'";

            return $this->naftaAccountsSearch($query);
        }

        return new WSResponse(Response::HTTP_BAD_REQUEST, 'Bad request');
    }

    /**
     * Select data from Gigya API.
     *
     * @return WSResponse
     */
    public function accountsSearch(string $query)
    {
        try {
            $query = [
                'userKey' => $this->userKey,
                'secret' => $this->secretKey,
                'query' => $query,
            ];
            $endpoint = $this->connector->getEndpoint('accounts.search');

            return $this->connector->call(Request::METHOD_POST, $endpoint, ['query' => $query]);
        } catch (\Exception $e) {
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    public function naftaAccountsSearch(string $query)
    {
        try {
            $query = [
                'userKey' => $this->naftaUserKey,
                'secret' => $this->naftaSecretKey,
                'query' => $query,
            ];
            $endpoint = $this->naftaGigyaApiConnector->getEndpoint('accounts.search');

            return $this->naftaGigyaApiConnector->call(Request::METHOD_POST, $endpoint, ['query' => $query]);
        } catch (\Exception $e) {
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    /**
     * Get Gigya token by code.
     */
    public function getGigyaToken(string $code, string $redirectUrl): WSResponse
    {
        try {
            $headers = [
                'content-type' => 'application/x-www-form-urlencoded',
            ];
            $bodyParameters = [
                'grant_type' => 'authorization_code',
                'code' => $code,
                'redirect_uri' => $redirectUrl
            ];
            $endpoint = $this->connectorGlobal->getEndpoint();
            $wsResponse = $this->connectorGlobal->call(
                Request::METHOD_POST,
                $endpoint,
                [
                    'headers' => $headers,
                    'body' => $bodyParameters,
                    'verify_peer' => false,
                    'auth_basic' => [$this->globalUserKey, $this->globalSecretKey]
                ]
            );
            if (Response::HTTP_OK == $wsResponse->getCode()) {
                $response = $wsResponse->getData();
                if (isset($response['access_token'])) {
                    return new WSResponse($wsResponse->getCode(), $response);
                }
                return new WSResponse(Response::HTTP_UNAUTHORIZED, $wsResponse->getData());
            }
            return new WSResponse($wsResponse->getCode(), $wsResponse->getData());
        } catch (\Exception $e) {
            $this->logger->error('Error CVS Access Token Exception ' . $e->getMessage());
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    /**
     * Get Consumer Rights.
     */
    public function getConsumerRights(string $userDbId, string $vin): WSResponse
    {
        $query = [
            'vin' => $vin,
            'customer_id' => $userDbId,
            'client_id' => $this->clientId
        ];
        $headers = [
            'content-type' => "application/json"
        ];

        $maxRetries = 3;
        $retryDelay = 1;
        
        for ($i = 0; $i < $maxRetries; $i++) {
            try {
                $options = $this->options;
                $options['query'] = $query;
                $options['headers'] = $headers;
                $response = $this->internalConnector->call(Request::METHOD_GET,
                    '/private/application/cvs/customerrights/v1/consumer-rights/lean',
                    $options);
                if ($response->getCode() != 200 && $maxRetries > $i+1) {
                    sleep($retryDelay);
                    continue;
                }
                return $response;
            } catch (\Exception $e) {
                $this->logger->error('Error Consumer Rights Exception ' . $e->getMessage());
                return new WSResponse($e->getCode(), $e->getMessage());
            }
        }
        return new WSResponse(Response::HTTP_BAD_REQUEST, 'Consumer Rights API unavailable after retries');
    }

    private function checkValue($value)
    {
        $value = trim($value);
        $value = stripslashes($value);
        $value = htmlspecialchars($value);

        return $value;
    }

    private function getCertifFile(string $certifName, string $extension): string
    {
        $fileWithExtension = "";
        if (substr($certifName, -strlen($extension)) == $extension) {
            $fileWithExtension = $certifName;
        } else {
            $fileWithExtension = $certifName . $extension;
        }

        return $this->certDir . '/' . $fileWithExtension;
        ;
    }
}

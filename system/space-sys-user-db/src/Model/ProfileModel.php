<?php
namespace App\Model;

class ProfileModel {
    private string $civility;
    private string $lastName;
    private string $firstName;
    private string $geopoint;
    private string $address2;
    private string $town;
    private string $countryCode;
    private string $postCode;
    private string $phoneNumber;
    private string $primaryEmail;
    private string $email;

    /**
     * Get the value of civility
     */ 
    public function getCivility()
    {
        return $this->civility;
    }

    /**
     * Set the value of civility
     *
     * @return  self
     */ 
    public function setCivility($civility)
    {
        $this->civility = $civility;

        return $this;
    }

    /**
     * Get the value of lastName
     */ 
    public function getLastName()
    {
        return $this->lastName;
    }

    /**
     * Set the value of lastName
     *
     * @return  self
     */ 
    public function setLastName($lastName)
    {
        $this->lastName = $lastName;

        return $this;
    }

    /**
     * Get the value of firstName
     */ 
    public function getFirstName()
    {
        return $this->firstName;
    }

    /**
     * Set the value of firstName
     *
     * @return  self
     */ 
    public function setFirstName($firstName)
    {
        $this->firstName = $firstName;

        return $this;
    }


    public function toArray(): array
    {
        $data = [
            'civility' => $this->civility,
            'lastName' => $this->lastName,
            'firstName' => $this->firstName,
            'address1' => $this->geopoint,
            'address2' => $this->address2,
            'country' => $this->countryCode,
            'city' => $this->town,
            'zipcode' => $this->postCode,
            'phone' => $this->phoneNumber,
            'email' => $this->email
        ];

        return array_filter($data, function ($value) {
            return $value !== null;
        });
    }

    /**
     * Get the value of geopoint
     */ 
    public function getGeopoint()
    {
        return $this->geopoint;
    }

    /**
     * Set the value of geopoint
     *
     * @return  self
     */ 
    public function setGeopoint($geopoint)
    {
        $this->geopoint = $geopoint;

        return $this;
    }

    /**
     * Get the value of address2
     */ 
    public function getAddress2()
    {
        return $this->address2;
    }

    /**
     * Set the value of address2
     *
     * @return  self
     */ 
    public function setAddress2($address2)
    {
        $this->address2 = $address2;

        return $this;
    }

    /**
     * Get the value of town
     */ 
    public function getTown()
    {
        return $this->town;
    }

    /**
     * Set the value of town
     *
     * @return  self
     */ 
    public function setTown($town)
    {
        $this->town = $town;

        return $this;
    }

    /**
     * Get the value of countryCode
     */ 
    public function getCountryCode()
    {
        return $this->countryCode;
    }

    /**
     * Set the value of countryCode
     *
     * @return  self
     */ 
    public function setCountryCode($countryCode)
    {
        $this->countryCode = $countryCode;

        return $this;
    }

    /**
     * Get the value of postCode
     */ 
    public function getPostCode()
    {
        return $this->postCode;
    }

    /**
     * Set the value of postCode
     *
     * @return  self
     */ 
    public function setPostCode($postCode)
    {
        $this->postCode = $postCode;

        return $this;
    }

    /**
     * Get the value of phoneNumber
     */ 
    public function getPhoneNumber()
    {
        return $this->phoneNumber;
    }

    /**
     * Set the value of phoneNumber
     *
     * @return  self
     */ 
    public function setPhoneNumber($phoneNumber)
    {
        $this->phoneNumber = $phoneNumber;

        return $this;
    }

    /**
     * Get the value of email
     */ 
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * Set the value of email
     *
     * @return  self
     */ 
    public function setEmail($email)
    {
        $this->email = $email;

        return $this;
    }
}
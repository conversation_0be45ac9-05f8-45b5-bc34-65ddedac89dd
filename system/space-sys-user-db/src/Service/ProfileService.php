<?php

namespace App\Service;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Component\Serializer\SerializerInterface;

use App\Helper\FileHelper;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use App\Model\AddVehicleModel;
use App\Model\UpdateVehicleModel;
use App\Connector\UserDataConnector;

class ProfileService
{
    use LoggerTrait;

    private string $authHttpLogin;
    private string $authHttpPassword;
    private string $certDir;
    private string $certName;
    private array $basicAuth;
    private UserDataConnector $userDataConnector;
    private TagAwareCacheAdapter $tagAwareCacheAdapter;
    private FileHelper $fileHelper;
    private SerializerInterface $serializer;

    public function __construct(
        string $authHttpLogin,
        string $authHttpPassword,
        string $certDir,
        string $certName,
        UserDataConnector $userDataConnector,
        TagAwareCacheAdapter $tagAwareCacheAdapter,
        FileHelper $fileHelper,
        SerializerInterface $serializer
    ) {
        $this->authHttpLogin = $authHttpLogin;
        $this->authHttpPassword = $authHttpPassword;
        $this->certDir = $certDir;
        $this->certName = $certName;
        $this->userDataConnector = $userDataConnector;
        $this->tagAwareCacheAdapter = $tagAwareCacheAdapter;
        $this->fileHelper = $fileHelper;
        $this->serializer = $serializer;
        $this->basicAuth = [$this->authHttpLogin, $this->authHttpPassword];
    }

    private function getCertifFile(string $certName, string $extension): string
    {
        $certPath = $this->certDir . '/' . $this->fileHelper->setExtension($certName, $extension);
        if (!file_exists($certPath)) {
            throw new \RuntimeException(sprintf('Certificate file not found: %s', $certPath));
        }
        return $certPath;
    }

    public function putUserData(string $id, ?array $data): WSResponse
    {
        $this->logger->info('Starting profile update', [
            'customerId' => $id
        ]);
        try {
            $options = [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'verify_peer' => false,
                'local_cert' => $this->getCertifFile($this->certName, '.cer'),
                'local_pk' => $this->getCertifFile($this->certName, '.key'),
                'auth_basic' => $this->basicAuth,
                'json' => $data
            ];
            $uri = 'api/v1/customer/' . $id;

            $wsResponse = $this->userDataConnector->call(Request::METHOD_PUT, $uri, $options);
            return $wsResponse;
        } catch (\Exception $e) {
            $this->logger->error('Error occurred during profile update', [
                'customerId' => $id,
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    public function putUserAddress(string $id, ?array $data): WSResponse
    {
        $this->logger->info('Starting profile address update', [
            'customerId' => $id
        ]);
        try {
            $options = [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'verify_peer' => false,
                'local_cert' => $this->getCertifFile($this->certName, '.cer'),
                'local_pk' => $this->getCertifFile($this->certName, '.key'),
                'auth_basic' => $this->basicAuth, # ["TA_UAT_INTG", "G1GBwd5u"],
                'json' => $data
            ];
            $uri = 'api/v1/customer/' . $id . '/address';

            $wsResponse = $this->userDataConnector->call(Request::METHOD_PUT, $uri, $options);
            return $wsResponse;
        } catch (\Exception $e) {
            $this->logger->error('Error occurred during profile update', [
                'customerId' => $id,
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    public function putUserContact(string $id, ?array $data): WSResponse
    {
        $this->logger->info('Starting profile address update', [
            'customerId' => $id
        ]);
        try {
            $data["deviceType"]="Mobile";
            $options = [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'verify_peer' => false,
                'local_cert' => $this->getCertifFile($this->certName, '.cer'),
                'local_pk' => $this->getCertifFile($this->certName, '.key'),
                'auth_basic' => $this->basicAuth, # ["TA_UAT_INTG", "G1GBwd5u"],
                'json' => $data
            ];
            $uri = 'api/v1/customer/' . $id . '/contact';

            $wsResponse = $this->userDataConnector->call(Request::METHOD_PUT, $uri, $options);
            return $wsResponse;
        } catch (\Exception $e) {
            $this->logger->error('Error occurred during profile update', [
                'customerId' => $id,
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    public function getUserData(string $id)
    {
        $this->logger->info('Starting profile get', [
            'id' => $id
        ]);
        try {
            $options = [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'verify_peer' => false,
                'local_cert' => $this->getCertifFile($this->certName, '.cer'),
                'local_pk' => $this->getCertifFile($this->certName, '.key'),
                'auth_basic' => $this->basicAuth
            ];
            $uri = 'api/v1/customer/' . $id;

            $wsResponse = $this->userDataConnector->call(Request::METHOD_GET, $uri, $options);
            return $wsResponse;
        } catch (\Exception $e) {
            $this->logger->error('Error occurred during profile get', [
                'customerId' => $id,
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }
}
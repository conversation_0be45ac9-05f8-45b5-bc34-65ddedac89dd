<?php

require_once __DIR__ . '/vendor/autoload.php';

use MongoDB\Client;

// MongoDB connection
$client = new Client("mongodb://spaceadmin:<EMAIL>");
$database = $client->selectDatabase('spaceDb');
$collection = $database->selectCollection('userData');

// Find the user document
$userId = '411d31789b35409fb3b75a4d59cec234';
$vin = 'VYEATTEN0SPU00175';

$userDocument = $collection->findOne(['userId' => $userId]);

if ($userDocument) {
    echo "User document found!\n";
    echo "User ID: " . $userDocument['userId'] . "\n";
    
    if (isset($userDocument['vehicle']) && is_array($userDocument['vehicle'])) {
        echo "Number of vehicles: " . count($userDocument['vehicle']) . "\n\n";
        
        foreach ($userDocument['vehicle'] as $index => $vehicle) {
            if (isset($vehicle['vin']) && $vehicle['vin'] === $vin) {
                echo "=== Vehicle Found (VIN: {$vin}) ===\n";
                echo json_encode($vehicle, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "\n";
                break;
            }
        }
    } else {
        echo "No vehicles found in user document.\n";
    }
} else {
    echo "User document not found for userId: $userId\n";
}


export const handler = async (event) => {
    // TODO implement
    const userAgent = event.headers?.['User-Agent'] || event.headers?.['user-agent'] || 'Unknown';
    const queryStringParameters = event['queryStringParameters'];
    console.log("String parametres ... ");
    console.log(JSON.stringify(queryStringParameters))
    console.log("User Agent ")
    console.log(userAgent)
   
    let targetUrl = "https://www.stellantis.com/"
   
    if (/android/i.test(userAgent)) {
      console.log("OS : AND");
      targetUrl = "https://play.google.com/store/apps/details?id="+queryStringParameters["apn"];
    } else if (/iphone|ipad|ipod/i.test(userAgent)) {
      console.log("OS: iOS : ");
      targetUrl = "https://apps.apple.com/app/"+queryStringParameters["isi"];
    }
  console.log("URL:"+targetUrl);
    const response = {
          statusCode: 302,
          headers: {
              Location: targetUrl
          }
      };
  
      return response;
  };
  
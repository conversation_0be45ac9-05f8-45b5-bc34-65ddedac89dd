<?php

namespace App\Service;

use App\Trait\LoggerTrait;
use App\Helper\WSResponse;
use App\Service\MongoAtlasQueryService;
use App\Model\FcmTokenRegisterModel;
use Symfony\Component\Serializer\SerializerInterface;

class RegisterService
{
    use LoggerTrait;

    public const COLLECTION = 'userData';

    private FcmTokenRegisterModel $fcmTokenRegisterModel;

    public function __construct(
        private MongoAtlasQueryService $mongoService,
        private SerializerInterface $serializer,
    ) {
    }

    /**
     * register FCM token to DB.
     */
    public function registerFcmToken(FcmTokenRegisterModel $fcmTokenRegisterModel, string $userId): WSResponse
    {
        $this->logger->info('Registering FCM token', ['userId' => $userId]);
        $dbUserDataId = $this->getUserData($userId);
        $this->fcmTokenRegisterModel = $fcmTokenRegisterModel;
        if (empty($dbUserDataId)) {
            $this->logger->error('User not found', ['userId' => $userId]);
            return new WSResponse(404, 'User not found');
        }
        else {
            $this->logger->info('User found', ['userId' => $userId]);
            $fcmToken = $this->getFcmTokenObject($dbUserDataId);
            if (!empty($fcmToken['documents'])) {
                $this->logger->info('FCM token already registered', ['userId' => $userId]);
                return $this->updateFcmToken($dbUserDataId);
            }
            return $this->insertFcmToken($dbUserDataId);
        }
    }

    /**
     * register session ID to DB.
     */
    public function registerSessionId(FcmTokenRegisterModel $fcmTokenRegisterModel, string $userId, string $sessionId): WSResponse
    {
        $this->logger->info('Registering session ID', ['userId' => $userId, 'sessionId' => $sessionId]);
        $dbUserDataId = $this->getUserData($userId);
        $this->fcmTokenRegisterModel = $fcmTokenRegisterModel;
        if (empty($dbUserDataId)) {
            $this->logger->error('User not found', ['userId' => $userId]);
            return new WSResponse(404, 'User not found', );
        }
        else {
            $this->logger->info('User found', ['userId' => $userId]);
            $fcmToken = $this->getFcmTokenObject($dbUserDataId);
            if (!empty($fcmToken['documents'])) {
                $this->logger->info('Session ID already registered', ['userId' => $userId]);
                return $this->updateSessionId($dbUserDataId, $sessionId, $fcmToken['documents'][0]['push'][0]);
            }
            return $this->insertSessionId($dbUserDataId, $sessionId);
        }
    }

    /**
     * get user data infos from DB.
     */
    public function getUserData(string $userId): string
    {
        $userData = $this->mongoService->find(self::COLLECTION, ['userId' => $userId]);
        $userData = json_decode($userData->getData(), true);
        return $userData['documents'][0]['_id'] ?? '';
    }

    /**
     * get fcm token object if exists
     */
    public function getFcmTokenObject(string $dbUserDataId): mixed
    {
        $pipeline = [
            ['$match' => ['_id' => ['$oid' => $dbUserDataId]]],
            ['$unwind' => '$push'],
            ['$match' => ['push.deviceId' => $this->fcmTokenRegisterModel->getDeviceId()]],
            [
                '$group' => [
                    '_id' => 'pushDetails',
                    'push' => ['$push' => '$push']
                ]
            ]
        ];
        $fcmToken = $this->mongoService->aggregate(self::COLLECTION, $pipeline);
        $fcmToken = $fcmToken->getData();

        return json_decode($fcmToken, true);
    }

    /**
     * register FCM token to DB.
     */
    public function insertFcmToken(string $dbUserDataId): WSResponse
    { 
        $filter = ['_id' => ['$oid' => $dbUserDataId]];
        $fields = ['push' => $this->getFcmTokenArray()];
        return $this->mongoService->updatePush(self::COLLECTION, $filter, $fields);
    }

    /**
     * insert an fcm token with sessionId
     */
    public function insertSessionId(string $dbUserDataId, string $sessionId): WSResponse
    {
        $filter = ['_id' => ['$oid' => $dbUserDataId]];
        $fcmToken = $this->getFcmTokenArray();
        $fcmToken['commandSession'] = [['remoteSessionId' => $sessionId, 'timestamp' => time()]];
        $fields = ['push' => $fcmToken];
        return $this->mongoService->updatePush(self::COLLECTION, $filter, $fields);
    }

    /**
     * update sessionId to DB.
     */
    public function updateSessionId(string $dbUserDataId, string $sessionId, array $fcmToken): WSResponse
    {
        $filter = ['_id' => ['$oid' => $dbUserDataId], 'push.deviceId' => $this->fcmTokenRegisterModel->getDeviceId()];
        $newCommandSession = ['remoteSessionId' => $sessionId, 'timestamp' => time()];
        $fields = [];
        if (isset($fcmToken['commandSession']) && is_array($fcmToken['commandSession'])) {
            $fcmToken['commandSession'][] = $newCommandSession;
            $fields["push.$.commandSession"] = $fcmToken['commandSession'];
        } else {
            $fields["push.$.commandSession"] = [$newCommandSession];
        }
        return $this->mongoService->updateOne(self::COLLECTION, $filter, $fields);
    }

    /**
     * update FCM token to DB.
     */
    public function updateFcmToken(string $dbUserDataId): WSResponse
    {
        $filter = ['_id' => ['$oid' => $dbUserDataId], 'push.deviceId' => $this->fcmTokenRegisterModel->getDeviceId()];
        $fields = $this->getFcmTokenFieldsToUpdate();
        return $this->mongoService->updateOne(self::COLLECTION, $filter, $fields);
    }

    /**
     * get fcm object like array to insert/update.
     */
    public function getFcmTokenFieldsToUpdate(): array
    {
        $fcmToken = $this->getFcmTokenArray();
        $exlus = ['deviceId'];
        $fields = [];
        foreach ($fcmToken as $key => $value) {
            if (in_array($key, $exlus)) {
                continue;
            }
            $fields["push.$.$key"] = $value;
        }
        return $fields;
    }

    /**
     * get fcm object like array to insert/update.
     */
    public function getFcmTokenArray(): array
    {
        $fcmToken = json_decode($this->serializer->serialize($this->fcmTokenRegisterModel, 'json'), true);

        return $fcmToken;
    }
}
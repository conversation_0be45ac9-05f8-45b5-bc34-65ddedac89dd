<?php

namespace App\Service;

use App\Trait\LoggerTrait;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Uid\Uuid;

class NotificationService
{
    use LoggerTrait;

    public const COLLECTION = 'notifications';
    public const USER_COLLECTION = 'userData';
    public const NOTIFICATION_TYPE_USER = 'user';
    public const NOTIFICATION_TYPE_VEHICLE = 'vehicle';
    private $notificationManager;

    public function __construct(
        private SerializerInterface $serializer,
        private MongoAtlasQueryService $mongoService
    ) {
    }

    /**
     * Get Notifications for a specific user
     *
     * @param string $userId
     * @param string $vin
     * @param array $data
     */
    public function getVehicleNotifications(string $userId, string $vin, array $data)
    {
        return $this->mongoService->aggregate(self::COLLECTION, $this->getVehicleNotificationsFilter($userId, $vin, $data));
    }

    /**
     * Build the pipeline for the aggregate query to filter notifications based on user, criticality, and type.
     *
     * @param string $userId
     * @param string $vin
     * @param array $data
     * @return array
     */
    public function getVehicleNotificationsFilter(string $userId, string $vin, array $data): array
    {
        $pipeline = [
            ['$match' => ['userId' => $userId]],
            ['$unwind' => '$messages']
        ];
        
        if (!empty($vin)) {
            $pipeline[] = ['$match' => ['messages.vin' => $vin]];
        }        

        // Filter by 'since' and 'till' based on Unix timestamp
        if (isset($data['since']) && $data['since'] !== null) {
            $timestampSince = (int)$data['since'];
            $pipeline[] = [
                '$match' => [
                    'messages.notificationData.timestamp' => ['$gte' => $timestampSince],
                ],
            ];
        }

        if (isset($data['till']) && $data['till'] !== null) {
            $timestampTill = (int)$data['till'];
            $pipeline[] = [
                '$match' => [
                    'messages.notificationData.timestamp' => ['$lte' => $timestampTill],
                ],
            ];
        }

        $pipeline[] = [
            '$sort' => [
                'messages.notificationData.timestamp' => -1, // Descending by timestamp
            ],
        ];

        // Using $facet to compute totalResults and paginated results separately
        $pipeline[] = [
            '$facet' => [
                'totalCount' => [
                    ['$count' => 'totalResults'] // Get total count before pagination
                ],
                'paginatedResults' => [
                    ['$skip' => (int)($data['offset'] ?? 0)], // Apply pagination offset
                    ['$limit' => (int)($data['limit'] ?? 10)], // Apply pagination limit
                    ['$group' => [
                        '_id' => null,
                        'count' => ['$sum' => 1], // Count after pagination
                        'items' => ['$push' => '$messages.notificationData']
                    ]]
                ]
            ]
        ];

        $pipeline[] = [
            '$project' => [
                'totalResults' => ['$arrayElemAt' => ['$totalCount.totalResults', 0]], // Extract totalResults
                'offset' => ['$literal' => (int)($data['offset'] ?? 0)], // Explicitly add offset
                'count' => [
                    '$ifNull' => [
                        ['$arrayElemAt' => ['$paginatedResults.count', 0]], 0
                    ]
                ], // Extract paginated count
                'vin' => ['$literal' => $vin], // Use `$literal` for static values
                'items' => [
                    '$ifNull' => [
                        ['$arrayElemAt' => ['$paginatedResults.items', 0]], []
                    ]
                ] // Extract items
            ]
        ];
        return $pipeline;
    }

    /**
     * Get Notifications for a specific user
     *
     * @param string $userId
     * @param array $data
     */
    public function getUserVehicleNotifications(string $userId, array $data)
    {
        return $this->mongoService->aggregate(self::COLLECTION, $this->getUserNotificationFilter($userId, $data));
    }

     /**
     * Build the pipeline for the aggregate query to filter notifications based on user, criticality, and type.
     *
     * @param string $userId
     * @param array $data
     * @return array
     */
    public function getUserNotificationFilter(string $userId, array $data): array
    {
        $pipeline = [
            ['$match' => ['userId' => $userId]],
            ['$unwind' => '$messages']
        ];
        
        // Filter by 'since' and 'till' based on Unix timestamp
        if (isset($data['since']) && $data['since'] !== null) {
            $timestampSince = (int)$data['since'];
            $pipeline[] = [
                '$match' => [
                    'messages.notificationData.timestamp' => ['$gte' => $timestampSince],
                ],
            ];
        }

        if (isset($data['till']) && $data['till'] !== null) {
            $timestampTill = (int)$data['till'];
            $pipeline[] = [
                '$match' => [
                    'messages.notificationData.timestamp' => ['$lte' => $timestampTill],
                ],
            ];
        }

        $pipeline[] = [
            '$sort' => [
                'messages.notificationData.timestamp' => -1, // Descending by timestamp
            ],
        ];

        // Group notifications by VIN
        $pipeline[] = [
            '$group' => [
                '_id' => '$messages.notificationData.vin',
                'count' => ['$sum' => 1],
                'items' => ['$push' => '$messages.notificationData']
            ],
        ];

        // Reshape the output
        $pipeline[] = [
            '$project' => [
                '_id' => 0,
                'vin' => '$_id',
                'count' => 1,
                'items' => 1
            ],
        ];
        
        return $pipeline;
    }

    /**
     * Save notifications for a specific user.
     * If the user ID exists in the collection, append new messages to the existing document's `messages` array.
     * Otherwise, create a new document with the `userId` and `messages`.
     *
     * @param string $userId
     * @param array $message
     * @param string $vin
     * @param string $type
     */
    public function saveNotification(string $type, string $userId, array $message, ?string $vin = null)   
    {
        try {
            // Adding TTL for 45 days
            $expiresAt = strtotime("+45 days", time());
            
            // Preparing the notification messages to fit the xf structure
            $formattedMessage = [];
            if ($type == self::NOTIFICATION_TYPE_USER) {
                $formattedMessage = $this->userXfFormattedNotification ($userId, $message);
            } elseif ($type == self::NOTIFICATION_TYPE_VEHICLE) {
                $formattedMessage = $this->vehicleXfFormattedNotification ($userId, $message, $vin);
            }
            
            $notification = [
                'notificationData' => $formattedMessage ?: [],
                'criticality'  => strtolower($message['criticality'] ?? ''),
                'type'         => $type,
                'vin'          => $vin ?? null,
                'expiresAt'    => $expiresAt
            ];  

            $filter = ['userId' => $userId];
            $result = $this->mongoService->updatePush(self::COLLECTION, $filter, ['messages' => $notification], true);

            $this->logger->info('Notifications successfully saved', [
                'userId'   => $userId,
                'messages' => $notification,
            ]);

            return $result;
        } catch (\Exception $e) {
            $this->logger->error('Failed to save notifications due to an exception: ' . $e->getMessage(), [
                'userId'   => $userId,
                'messages' => $message,
            ]);
        }
    }

    /**
     * Formats a user notification for XF format.
     *
     * @param string $userId  The unique identifier of the user.
     * @param array  $message The message data containing notification details.
     *
     * @return array The formatted notification data.
     */
    public function userXfFormattedNotification(string $userId, array $message) {
        $notificationData = [];
        $notification = [
            "context" => [
                "brandMarketingName" => $message['data']['context']['brandMarketingName'] ?? "",
                "firstName" => $message['data']['context']['firstName'] ?? '',
                "model" => $message['data']['context']['model']?? "",
                "nickname" => $message['data']['context']['nickname']?? "",
            ],
            "in-app" => [
                "body" => $message['notification']['body'] ?? '',
                "criticality" => $message['criticality'],
                "subtitle" => $message['notification']['subtitle'] ?? '',
                "title" => $message['notification']['title'] ?? ''
            ],
            "data" => [
                "notificationId" => $message['data']['serviceData']['Data']['notificationId'] ?? '',
                // "rcpdRequestId" => "",//Available in SSDP API response
                "userId" => $userId,
                // "response" => ""//Available in SSDP API response
            ],
            "click_action" => $message['apiPushConfig']['staticConfig']['android']['notification']['click_action'] ?? ""
        ];

        $notificationData ['id'] = Uuid::v4();
        $notificationData ['notification'] = $notification;
        // $notificationData ['read'] = false ;//Available in SSDP API response
        $notificationData ['timestamp'] = $message['data']['serviceData']['Timestamp'] ?? null;
        // $notificationData ['correlationId'] = "" ; //Available in SSDP API response
        $notificationData ['vin'] = $message['serviceData']["Data"]['eventName'] ?? "";

        return $notificationData;
    }

    /**
     * Formats a vehicle notification for XF format.
     *
     * @param string $userId  The unique identifier of the user.
     * @param array  $message The message data containing notification details.
     *
     * @return array The formatted notification data.
     */
    public function vehicleXfFormattedNotification(string $userId, array $message, string $vin) {
        $notificationData = [];
        $notification = [
            "context" => [
                "brandMarketingName" => $message['data']['context']['brandMarketingName'] ?? "",
                "firstName" => $message['data']['context']['firstName'] ?? '',
                "model" => $message['data']['context']['model']?? "",
                "nickname" => $message['data']['context']['nickname']?? "",
            ],
            "in-app" => [
                "criticality" => $message['criticality'],
                "body" => $message['notification']['body'] ?? '',
                "subtitle" => $message['notification']['subtitle'] ?? '',
                "title" => $message['notification']['title'] ?? '',
            ],
            "data" => [
                "notificationId" => $message['data']['serviceData']['Data']['notificationId'] ?? '',
                // "rcpdRequestId" => "",//Available in SSDP API response
                "userId" => $userId,
                // "response" => ""//Available in SSDP API response

            ],
            "click_action" => $message['apiPushConfig']['staticConfig']['android']['notification']['click_action'] ?? ""
        ];

        $notificationData ['id'] = Uuid::v4();
        $notificationData ['notification'] = $notification;
        $notificationData ['eventName'] = $message['data']['serviceData']['Data']['eventName'] ?? '';
        $notificationData ['timestamp'] = $message['data']['serviceData']['Timestamp'] ?? null;
        $notificationData ['vin'] = $vin ;
        // $notificationData ['correlationId'] = "" ; //Available in SSDP API response
        // $notificationData ['read'] = false ;//Available in SSDP API response
        
        return $notificationData;
    }
}
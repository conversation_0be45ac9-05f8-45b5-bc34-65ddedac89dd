<?php

namespace App\Service;

use App\Trait\LoggerTrait;
use App\Helper\WSResponse;

class UserService
{
    use LoggerTrait;

    public const COLLECTION = 'userData';

    public function __construct(
        private MongoAtlasQueryService $mongoService
    ) {}

    public function getUsersBySessionIdFilter(?string $userId, ?string $vin, ?string $sessionId)
    {
        return
            [
                [
                    '$match' => ['userId' => $userId, 'vehicle.vin' => $vin]
                ],
                ['$unwind' => '$push'],
                [
                    '$match' => [
                        'push.commandSession.remoteSessionId' => $sessionId,
                    ],
                ],
                [
                    '$group' => [
                        '_id' => 'pushDetails',
                        'push' => [
                            '$push' => '$push',
                        ],
                    ],
                ],
            ];
    }

    public function getUserByUserIdFilter(?string $userId)
    {
        return
            [
                [
                    '$match' => ['userId' => $userId],
                ],
            ];
    }

    public function getUsersBySessionId(?string $userId, ?string $vin, ?string $sessionId): WSResponse
    {
        $this->logger->info('Getting users', ['sessionId' => $sessionId]);
        return $this->mongoService->aggregate(self::COLLECTION, $this->getUsersBySessionIdFilter($userId, $vin, $sessionId));
    }

    public function getUserByUserId(?string $userId): WSResponse
    {
        $this->logger->info('Getting user', ['userId' => $userId]);
        return $this->mongoService->aggregate(self::COLLECTION, $this->getUserByUserIdFilter($userId));
    }
}

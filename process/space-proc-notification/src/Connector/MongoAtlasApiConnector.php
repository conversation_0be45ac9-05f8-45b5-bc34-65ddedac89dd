<?php

namespace App\Connector;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Contracts\HttpClient\HttpClientInterface;

/**
 * The connector with the AtlasMongoDB API.
 */
class MongoAtlasApiConnector
{
    use LoggerTrait;

    public function __construct(
        private HttpClientInterface $client,
        private string $mongoApp
    ) {
    }

    /**
     * Call Mongo Atlas api.
     */
    public function call(string $method, string $endpoint, array $options = []): WSResponse
    {
        try {
            $response = $this->client->request($method, $endpoint, $options);
            $this->logger->info(__METHOD__.' '.$endpoint.' options '.json_encode($options));

            return new WSResponse($response->getStatusCode(), $response->getContent(false));
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__.' : Cached Exception '.$e->getMessage(), [
                $method,
                'url' => $endpoint,
                $options,
            ]);

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    /**
     * build mongoAtlas endpoint.
     */
    public function getEndpoint(string $action): string
    {
        return sprintf('/app/%s/endpoint/data/v1/action/%s', $this->mongoApp, $action);
    }
}

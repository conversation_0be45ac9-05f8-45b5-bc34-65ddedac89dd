# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    mongo_db.app: "%env(MONGO_APP)%"
    mongo_db.database: "%env(MONGO_DATABASE)%"
    mongo_db.datasource: "%env(MONGO_DATASOURCE)%"
    
services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'
    
    App\Service\MongoAtlasQueryService:
        arguments:
            $database: '%mongo_db.database%'
            $dataSource: '%mongo_db.datasource%'

    App\Connector\MongoAtlasApiConnector:
        $mongoApp: '%mongo_db.app%'
    
    App\Connector\SystemFirebaseConnector:
        arguments:
            $url: "%env(MS_SYS_FIREBASE_URL)%"
    
    Symfony\Component\Messenger\Transport\TransportInterface:
        tags: [messenger.space_vehicle_notification_sqs_queue,
                messenger.space_user_notification_sqs_queue
                ]

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

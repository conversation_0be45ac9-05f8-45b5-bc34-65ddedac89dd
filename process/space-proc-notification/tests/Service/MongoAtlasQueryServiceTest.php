<?php

namespace App\Tests\Service;

use App\Connector\MongoAtlasApiConnector;
use App\Helper\WSResponse;
use App\Model\MongoQueryModel;
use App\Service\MongoAtlasQueryService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

/**
 * Tests for the MongoAtlasQueryService class
 *
 * This test suite covers all scenarios for the MongoAtlasQueryService:
 * - Constructor and initialization
 * - Building queries
 * - All query methods (find, aggregate, updateMany, updateOne, updatePush, insertOne, insertMany)
 * - Error handling
 */
class MongoAtlasQueryServiceTest extends TestCase
{
    private MongoAtlasQueryService $service;
    private MongoAtlasApiConnector $connector;
    private NormalizerInterface $normalizer;
    private LoggerInterface $logger;
    private string $testDataSource;
    private string $testDatabase;
    private string $testCollection;

    /**
     * Set up the test environment before each test
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->testDataSource = 'testDataSource';
        $this->testDatabase = 'testDatabase';
        $this->testCollection = 'testCollection';

        $this->connector = $this->createMock(MongoAtlasApiConnector::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->logger->expects($this->any())
            ->method('info');
        $this->logger->expects($this->any())
            ->method('error');
        $this->connector->setLogger($this->logger);

        $this->normalizer = $this->createMock(NormalizerInterface::class);

        $this->service = new MongoAtlasQueryService(
            $this->normalizer,
            $this->connector,
            $this->testDataSource,
            $this->testDatabase
        );
        $this->service->setLogger($this->logger);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        unset($this->service);
        unset($this->connector);
        unset($this->normalizer);
        unset($this->logger);    
    }

    /**
     * Test the constructor initializes the query model correctly
     */
    public function testConstructor(): void
    {
        // Create a new service to test the constructor
        $service = new MongoAtlasQueryService(
            $this->normalizer,
            $this->connector,
            $this->testDataSource,
            $this->testDatabase
        );

        // Use reflection to access the private query property
        $reflection = new \ReflectionClass($service);
        $property = $reflection->getProperty('query');
        $property->setAccessible(true);
        $query = $property->getValue($service);

        // Verify the query is initialized correctly
        $this->assertInstanceOf(MongoQueryModel::class, $query);
        $this->assertEquals($this->testDataSource, $query->getDataSource());
        $this->assertEquals($this->testDatabase, $query->getDatabase());
    }

    /**
     * Test the buildQuery static method
     */
    public function testBuildQuery(): void
    {
        $query = MongoAtlasQueryService::buildQuery($this->testDataSource, $this->testDatabase);

        $this->assertInstanceOf(MongoQueryModel::class, $query);
        $this->assertEquals($this->testDataSource, $query->getDataSource());
        $this->assertEquals($this->testDatabase, $query->getDatabase());
    }

    /**
     * Test the find method with a filter
     */
    public function testFindWithFilter(): void
    {
        $filter = [
            'field1' => 'value1',
            'field2' => 'value2',
        ];

        $normalizedData = [
            'dataSource' => $this->testDataSource,
            'database' => $this->testDatabase,
            'collection' => $this->testCollection,
            'filter' => $filter,
        ];

        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with('find')
            ->willReturn('testEndpoint');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', 'find']])
            ->willReturn($normalizedData);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_POST, 'testEndpoint', ['json' => $normalizedData])
            ->willReturn(new WSResponse(Response::HTTP_OK, 'testResponse'));

        $response = $this->service->find($this->testCollection, $filter);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    /**
     * Test the find method with null filter
     */
    public function testFindWithNullFilter(): void
    {
        $normalizedData = [
            'dataSource' => $this->testDataSource,
            'database' => $this->testDatabase,
            'collection' => $this->testCollection,
            'filter' => [],
        ];

        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with('find')
            ->willReturn('testEndpoint');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', 'find']])
            ->willReturn($normalizedData);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_POST, 'testEndpoint', ['json' => $normalizedData])
            ->willReturn(new WSResponse(Response::HTTP_OK, 'testResponse'));

        $response = $this->service->find($this->testCollection, null);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    /**
     * Test the aggregate method with a pipeline
     */
    public function testAggregateWithPipeline(): void
    {
        $pipeline = [
            ['$match' => ['status' => 'active']],
            ['$sort' => ['createdAt' => -1]],
        ];

        $normalizedData = [
            'dataSource' => $this->testDataSource,
            'database' => $this->testDatabase,
            'collection' => $this->testCollection,
            'pipeline' => $pipeline,
        ];

        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with('aggregate')
            ->willReturn('testEndpoint');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', 'aggregate']])
            ->willReturn($normalizedData);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_POST, 'testEndpoint', ['json' => $normalizedData])
            ->willReturn(new WSResponse(Response::HTTP_OK, 'testResponse'));

        $response = $this->service->aggregate($this->testCollection, $pipeline);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    /**
     * Test the aggregate method with null pipeline
     */
    public function testAggregateWithNullPipeline(): void
    {
        $normalizedData = [
            'dataSource' => $this->testDataSource,
            'database' => $this->testDatabase,
            'collection' => $this->testCollection,
            'pipeline' => [],
        ];

        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with('aggregate')
            ->willReturn('testEndpoint');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', 'aggregate']])
            ->willReturn($normalizedData);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_POST, 'testEndpoint', ['json' => $normalizedData])
            ->willReturn(new WSResponse(Response::HTTP_OK, 'testResponse'));

        $response = $this->service->aggregate($this->testCollection, null);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    /**
     * Test the updateMany method
     */
    public function testUpdateMany(): void
    {
        $filter = ['userId' => '123'];
        $fields = ['status' => 'active', 'updatedAt' => '2023-01-01'];
        $upsert = true;

        $normalizedData = [
            'dataSource' => $this->testDataSource,
            'database' => $this->testDatabase,
            'collection' => $this->testCollection,
            'filter' => $filter,
            'update' => ['$set' => $fields],
            'upsert' => $upsert,
        ];

        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with('updateMany')
            ->willReturn('testEndpoint');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', 'updateMany']])
            ->willReturn($normalizedData);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_POST, 'testEndpoint', ['json' => $normalizedData])
            ->willReturn(new WSResponse(Response::HTTP_OK, 'testResponse'));

        $response = $this->service->updateMany($this->testCollection, $filter, $fields, $upsert);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    /**
     * Test the updateMany method with default upsert value
     */
    public function testUpdateManyWithDefaultUpsert(): void
    {
        $filter = ['userId' => '123'];
        $fields = ['status' => 'active', 'updatedAt' => '2023-01-01'];

        $normalizedData = [
            'dataSource' => $this->testDataSource,
            'database' => $this->testDatabase,
            'collection' => $this->testCollection,
            'filter' => $filter,
            'update' => ['$set' => $fields],
            'upsert' => false,
        ];

        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with('updateMany')
            ->willReturn('testEndpoint');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', 'updateMany']])
            ->willReturn($normalizedData);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_POST, 'testEndpoint', ['json' => $normalizedData])
            ->willReturn(new WSResponse(Response::HTTP_OK, 'testResponse'));

        $response = $this->service->updateMany($this->testCollection, $filter, $fields);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    /**
     * Test the updateOne method
     */
    public function testUpdateOne(): void
    {
        $filter = ['userId' => '123'];
        $fields = ['status' => 'active', 'updatedAt' => '2023-01-01'];
        $upsert = true;

        $normalizedData = [
            'dataSource' => $this->testDataSource,
            'database' => $this->testDatabase,
            'collection' => $this->testCollection,
            'filter' => $filter,
            'update' => ['$set' => $fields],
            'upsert' => $upsert,
        ];

        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with('updateOne')
            ->willReturn('testEndpoint');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', 'updateOne']])
            ->willReturn($normalizedData);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_POST, 'testEndpoint', ['json' => $normalizedData])
            ->willReturn(new WSResponse(Response::HTTP_OK, 'testResponse'));

        $response = $this->service->updateOne($this->testCollection, $filter, $fields, $upsert);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    /**
     * Test the updatePush method
     */
    public function testUpdatePush(): void
    {
        $filter = ['userId' => '123'];
        $fields = ['notifications' => ['id' => 'notif1', 'message' => 'New notification']];
        $upsert = true;

        $normalizedData = [
            'dataSource' => $this->testDataSource,
            'database' => $this->testDatabase,
            'collection' => $this->testCollection,
            'filter' => $filter,
            'update' => ['$push' => $fields],
            'upsert' => $upsert,
        ];

        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with('updateOne')
            ->willReturn('testEndpoint');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', 'updateOne']])
            ->willReturn($normalizedData);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_POST, 'testEndpoint', ['json' => $normalizedData])
            ->willReturn(new WSResponse(Response::HTTP_OK, 'testResponse'));

        $response = $this->service->updatePush($this->testCollection, $filter, $fields, $upsert);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    /**
     * Test the insertOne method
     */
    public function testInsertOne(): void
    {
        $document = [
            'userId' => '123',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'createdAt' => '2023-01-01',
        ];

        $normalizedData = [
            'dataSource' => $this->testDataSource,
            'database' => $this->testDatabase,
            'collection' => $this->testCollection,
            'document' => $document,
        ];

        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with('insertOne')
            ->willReturn('testEndpoint');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', 'insertOne']])
            ->willReturn($normalizedData);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_POST, 'testEndpoint', ['json' => $normalizedData])
            ->willReturn(new WSResponse(Response::HTTP_OK, 'testResponse'));

        $response = $this->service->insertOne($this->testCollection, $document);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    /**
     * Test the insertMany method
     */
    public function testInsertMany(): void
    {
        $documents = [
            [
                'userId' => '123',
                'name' => 'John Doe',
                'email' => '<EMAIL>',
            ],
            [
                'userId' => '456',
                'name' => 'Jane Doe',
                'email' => '<EMAIL>',
            ],
        ];

        $normalizedData = [
            'dataSource' => $this->testDataSource,
            'database' => $this->testDatabase,
            'collection' => $this->testCollection,
            'documents' => $documents,
        ];

        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->with('insertMany')
            ->willReturn('testEndpoint');

        $this->normalizer
            ->expects($this->once())
            ->method('normalize')
            ->with($this->isInstanceOf(MongoQueryModel::class), 'array', ['groups' => ['default', 'insertMany']])
            ->willReturn($normalizedData);

        $this->connector
            ->expects($this->once())
            ->method('call')
            ->with(Request::METHOD_POST, 'testEndpoint', ['json' => $normalizedData])
            ->willReturn(new WSResponse(Response::HTTP_OK, 'testResponse'));

        $response = $this->service->insertMany($this->testCollection, $documents);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());
        $this->assertEquals('testResponse', $response->getData());
    }

    /**
     * Test error handling in the execute method
     */
    public function testExecuteWithException(): void
    {
        $exception = new \Exception('Test exception', 500);

        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->willThrowException($exception);

        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('MongoAtlasQueryService::execute Catched Exception:Test exception'));

        $response = $this->service->find($this->testCollection, []);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(500, $response->getCode());
        $this->assertEquals('Test exception', $response->getData());
    }

    /**
     * Test error handling with a different exception
     */
    public function testExecuteWithDifferentException(): void
    {
        $exception = new \RuntimeException('Runtime exception', 400);

        $this->connector
            ->expects($this->once())
            ->method('getEndpoint')
            ->willThrowException($exception);

        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('MongoAtlasQueryService::execute Catched Exception:Runtime exception'));

        $response = $this->service->find($this->testCollection, []);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(400, $response->getCode());
        $this->assertEquals('Runtime exception', $response->getData());
    }
}

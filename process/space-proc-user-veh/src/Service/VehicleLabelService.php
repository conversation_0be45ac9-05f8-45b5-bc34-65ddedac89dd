<?php

namespace App\Service;

use App\Helper\LcdvsProvider;
use App\Helper\WSResponse;
use App\MongoDB\UserData\UserDataDocument\Vehicle;
use App\MongoDB\VehicleLabel\VehicleLabelDocument;
use App\MongoDB\VehicleLabel\VehicleLabelDocumentCollection;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\VehicleLabel;
use Space\MongoDocuments\Service\MongoDBService;
use Symfony\Component\Serializer\SerializerInterface;

class VehicleLabelService
{
    use LoggerTrait;

    public const COLLECTION = 'vehicleLabel';

    public function __construct(
        private MongoDBService $mongoDBService,
        private SerializerInterface $serializer
    )
    {
    }

    public function getVehicleLabelDocumentByLcdv(string $lcdvCode): WSResponse
    {
        $this->logger->info('Getting vehicle label document by LCDV', ['lcdv' => $lcdvCode]);

        try {
            // Use the VehicleLabelRepository to find by LCDV with longest match
            $vehicleLabel = $this->mongoDBService->getDocumentManager()
                ->getRepository(VehicleLabel::class)
                ->findByLcdvWithLongestMatch($lcdvCode);

            if ($vehicleLabel) {
                // Convert ODM document to legacy format for backward compatibility
                $document = [
                    '_id' => $vehicleLabel->getId(),
                    'lcdv' => $vehicleLabel->getLcdv(),
                    'label' => $vehicleLabel->getLabel(),
                    'isO2X' => $vehicleLabel->getIsO2X(),
                    'sdp' => $vehicleLabel->getSdp(),
                    'visualSettings' => $vehicleLabel->getVisualSettings()
                ];

                $responseData = ['documents' => [$document]];
            } else {
                $responseData = ['documents' => []];
            }

            return new WSResponse(200, json_encode($responseData));

        } catch (\Exception $e) {
            $this->logger->error('Error getting vehicle label document by LCDV', [
                'lcdv' => $lcdvCode,
                'exception' => $e->getMessage()
            ]);
            return new WSResponse(500, json_encode(['error' => 'Internal server error']));
        }


    }

    public function findLabelByLongestMatchingLcdv(string $lcdvCode): ?string
    {
        $response = $this->getVehicleLabelDocumentByLcdv($lcdvCode);

        try {
            $documents = $this->serializer->deserialize($response->getData(), VehicleLabelDocumentCollection::class, 'json')->getDocuments();
        } catch (\Exception $e) {
            $this->logger->error('Error while deserializing vehicle label documents', ['exception' => $e]);
            return null;
        }
        
        $document = $documents[0] ?? null;
        return $document?->label;
    }
}
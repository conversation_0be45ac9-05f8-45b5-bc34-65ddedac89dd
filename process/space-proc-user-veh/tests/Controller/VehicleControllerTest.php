<?php

declare(strict_types=1);

namespace App\Tests\Controller;

use App\Controller\VehicleController;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Manager\MyMarqueVehicleManager;
use App\Manager\VehicleManager;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class VehicleControllerTest extends WebTestCase
{
    protected function setup(): void
    {
        parent::setup();
    }

    public function testVehicleSummaryWithSuccessResponse(): void
    {
        $vehicleManagerMock = $this->createMock(VehicleManager::class);

        $success = new SuccessResponse('Success result', Response::HTTP_OK);
        $vehicleManagerMock->method('getVehicleSummary')
            ->WillReturn($success);

        $client = static::createClient([]);
        $container = $client->getContainer();

        $validator = $container->get(ValidatorInterface::class);
        $userId = 1;
        $vehicleId = '123';
        $url = 'https://api-sys-idp.space.com/v1/orders/{orderFormId}/summary';
        $request = Request::create($url, Request::METHOD_GET, [], [], [], ['HTTP_USERID' => $userId]);

        $controller = $container->get(VehicleController::class);
        $response = $controller->VehicleSummary($validator, $vehicleManagerMock, $request, $vehicleId);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $encoded = $response->getContent();
        $data = json_decode($encoded, true);

        $this->assertIsArray($data);
        $this->assertArrayHasKey('success', $data);
    }

    public function testVehicleSummaryWithError(): void
    {
        $vehicleManagerMock = $this->createMock(VehicleManager::class);

        $vehicleManagerMock->expects($this->never())
            ->method('getVehicleSummary');
        $client = static::createClient([]);
        $container = $client->getContainer();

        $validator = $container->get(ValidatorInterface::class);
        $vehicleId = '123';
        $url = 'https://api-sys-idp.space.com/v1/orders/{orderFormId}/summary';
        $request = Request::create($url, Request::METHOD_GET, [], [], [], []);

        $controller = $container->get(VehicleController::class);
        $response = $controller->VehicleSummary($validator, $vehicleManagerMock, $request, $vehicleId);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);

        $error = $responseData['error'] ?? null;
        $this->assertIsArray($error);

        $errors = $error['errors'] ?? null;
        $this->assertIsArray($errors);

        $this->assertArrayHasKey('userId', $errors);
        $this->assertSame($errors['userId'], 'This value should not be blank.');
    }

    public function testGetVehicleSuccessResponse(): void
    {
        //$this->markTestSkipped('The api was disabled; will be removed in the future');
        $validator = $this->createMock(ValidatorInterface::class);
        $request = $this->createMock(Request::class);
        $headerBag = $this->createMock(HeaderBag::class);
        $request->headers = $headerBag;
        $vehicleManager = $this->createMock(VehicleManager::class);
        $controller = $this->getContainer()->get(VehicleController::class);
        $headerBag->expects($this->once())
            ->method('get')
            ->willReturn('someheader');

        $validator->expects($this->once())
            ->method('validate')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        $vehicleManager->expects($this->once())
            ->method('getVehicles')
            ->willReturn(new SuccessResponse(['vehicles' => []], 200));

        $response = $controller->index($validator, $vehicleManager, $request);
        $expectedResponse = new JsonResponse(['success' => ['vehicles' => []]], Response::HTTP_OK);

        $this->assertSame($response->getContent(), $expectedResponse->getContent());
    }

    public function testGetVehicleResponseFormat(): void
    {
        // Test to verify the exact response format matches user requirements
        $validator = $this->createMock(ValidatorInterface::class);
        $request = $this->createMock(Request::class);
        $headerBag = $this->createMock(HeaderBag::class);
        $request->headers = $headerBag;
        $vehicleManager = $this->createMock(VehicleManager::class);
        $controller = $this->getContainer()->get(VehicleController::class);

        $headerBag->expects($this->once())
            ->method('get')
            ->willReturn('test-user-id');

        $validator->expects($this->once())
            ->method('validate')
            ->willReturn($this->createMock(ConstraintViolationListInterface::class));

        // Mock vehicle data in the expected format
        $mockVehicleData = [
            [
                'id' => '35700061-8db4-4c9a-87bf-70f382bbec27',
                'vin' => 'FCAHBTTG6P9004681',
                'label' => 'C4 BERLINE 5 Portes',
                'versionId' => '1PP2A5HMT1B0A0B0',
                'brand' => 'AC',
                'visual' => 'https://visuel3d-secure.citroen.com/v3dimage.ashx?...',
                'language' => null,
                'country' => null,
                'isOrder' => true,
                'vehicleOrder' => [
                    'mopId' => 'NjQwZjM3ODBjZTNlNDY3MGNmNTg4NTAx',
                    'orderFormId' => '566777',
                    'trackingStatus' => 'ORDERFORM_VALIDATED',
                    'isUpdated' => false,
                    'orderFormStatus' => 'VALIDATED'
                ]
            ]
        ];

        $vehicleManager->expects($this->once())
            ->method('getVehicles')
            ->willReturn(new SuccessResponse(['vehicles' => $mockVehicleData], 200));

        $response = $controller->index($validator, $vehicleManager, $request);

        // Verify the response format matches user requirements: {"success": {"vehicles": [...]}}
        $responseData = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('success', $responseData);
        $this->assertArrayHasKey('vehicles', $responseData['success']);
        $this->assertIsArray($responseData['success']['vehicles']);
        $this->assertCount(1, $responseData['success']['vehicles']);

        $vehicle = $responseData['success']['vehicles'][0];
        $this->assertEquals('35700061-8db4-4c9a-87bf-70f382bbec27', $vehicle['id']);
        $this->assertEquals('FCAHBTTG6P9004681', $vehicle['vin']);
        $this->assertEquals('C4 BERLINE 5 Portes', $vehicle['label']);
        $this->assertEquals('1PP2A5HMT1B0A0B0', $vehicle['versionId']);
        $this->assertEquals('AC', $vehicle['brand']);
        $this->assertTrue($vehicle['isOrder']);
        $this->assertArrayHasKey('vehicleOrder', $vehicle);

        $vehicleOrder = $vehicle['vehicleOrder'];
        $this->assertEquals('NjQwZjM3ODBjZTNlNDY3MGNmNTg4NTAx', $vehicleOrder['mopId']);
        $this->assertEquals('566777', $vehicleOrder['orderFormId']);
        $this->assertEquals('ORDERFORM_VALIDATED', $vehicleOrder['trackingStatus']);
        $this->assertFalse($vehicleOrder['isUpdated']);
        $this->assertEquals('VALIDATED', $vehicleOrder['orderFormStatus']);
    }

    public function testVehicleOrderWithSuccess(): void
    {
        $successResponse = new SuccessResponse('Success response', Response::HTTP_OK);
        $vehicleManagerMock = $this->createMock(VehicleManager::class);
        $vehicleManagerMock
            ->expects($this->once())
            ->method('createOrUpdateVehicle')
            ->willReturn($successResponse);

        $content = [
            'userId' => 'userId-1234',
            'vin' => 'VIN-234',
            'brand' => 'OP',
            'visual' => '',
            'culture' => 'fr_FR',
            'label' => 'Order label',
            'versionId' => '1.2.3',
            'mopId' => 'MOP-345',
            'orderFormId' => '566777',
            'trackingStatus' => 'PRODUCTION_START',
            'orderFormStatus' => 'VALIDATED',
        ];
        $container = $this->getContainer();

        $url = '/v1/vehicles/new';
        $request = Request::create($url, Request::METHOD_POST, [], [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($content));

        $validator = $container->get(ValidatorInterface::class);

        // Use the container to retrieve the controller
        $controller = $container->get(VehicleController::class);
        $response = $controller->VehicleOrder($validator, $request, $vehicleManagerMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertStringContainsString('success', $response->getContent());
    }

    public function testVehicleOrderWithError(): void
    {
        $vehicleManagerMock = $this->createMock(VehicleManager::class);
        $content = [];
        $container = $this->getContainer();

        $url = '/v1/vehicles/new';
        $request = Request::create($url, Request::METHOD_GET, [], [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($content));

        $validator = $container->get(ValidatorInterface::class);

        // Use the container to retrieve the controller
        $controller = $container->get(VehicleController::class);
        $response = $controller->VehicleOrder($validator, $request, $vehicleManagerMock);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $this->assertStringContainsString('This value should not be blank.', $response->getContent());
    }

    public function testVehicleSummaryWithSuccessV2(): void
    {
        $vehicleId = '123';
        $userId = '456';
        $successResponse = new SuccessResponse('Success response', Response::HTTP_OK);
        $vehicleManagerMock = $this->createMock(VehicleManager::class);
        $vehicleManagerMock
            ->expects($this->once())
            ->method('getVehicleSummary')
            ->willReturn($successResponse);

        $container = $this->getContainer();

        $url = "/v1/vehicles/{$vehicleId}/summary";
        $request = Request::create($url, Request::METHOD_GET, [], [], [], ['HTTP_USERID' => $userId]);

        $validator = $container->get(ValidatorInterface::class);

        // Use the container to retrieve the controller
        $controller = $container->get(VehicleController::class);
        $response = $controller->VehicleSummary($validator, $vehicleManagerMock, $request, $vehicleId);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertStringContainsString('success', $response->getContent());
    }

    public function testVehicleSummaryWithErrorV2(): void
    {
        $vehicleId = '123';
        $userId = '';
        $successResponse = new SuccessResponse('Success response', Response::HTTP_OK);
        $vehicleManagerMock = $this->createMock(VehicleManager::class);

        $container = $this->getContainer();

        $url = "/v1/vehicles/{$vehicleId}/summary";
        $request = Request::create($url, Request::METHOD_GET, [], [], [], ['HTTP_USERID' => $userId]);

        $validator = $container->get(ValidatorInterface::class);

        // Use the container to retrieve the controller
        $controller = $container->get(VehicleController::class);
        $response = $controller->VehicleSummary($validator, $vehicleManagerMock, $request, $vehicleId);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $this->assertStringContainsString('"userId":"This value should not be blank."', $response->getContent());
    }

    private function callAddVehicle(MyMarqueVehicleManager $myMarqueVehicleManager): JsonResponse
    {
        //$this->markTestSkipped('The api was disabled; will be removed in the future');
        $parameters = [
            'brand' => 'OP',
            'country' => 'FR',
            'language' => 'fr',
            'source' => 'myopel',
        ];
        $content = [
            'vin' => 'VIN-234',
            'mileage' => '123',
        ];

        $container = $this->getContainer();

        $url = '/v1/vehicles/new';
        $request = Request::create(
            $url,
            Request::METHOD_POST,
            $parameters,
            [],
            [],
            ['CONTENT_TYPE' => 'application/json'], json_encode($content)
        );

        // Use the container to retrieve the controller
        $controller = $container->get(VehicleController::class);
        $response = $controller->addVehicle($request, $myMarqueVehicleManager);

        return $response;
    }

    public function dataProviderAddVehicleWillReturnSuccessResponse(): array
    {
        return [
            [Response::HTTP_OK],
            [Response::HTTP_CREATED],
        ];
    }

    /**
     * @dataProvider dataProviderAddVehicleWillReturnSuccessResponse
     */
    public function testAddVehicleWillReturnSuccessResponse(int $responseCode): void
    {
        //$this->markTestSkipped('The api was disabled; will be removed in the future');
        $myMarqueVehicleManager = $this->createMock(MyMarqueVehicleManager::class);
        $myMarqueVehicleManager
            ->expects($this->once())
            ->method('addVehicleToUserGarage')
            ->willReturn(new SuccessResponse('Success response', $responseCode));

        $response = $this->callAddVehicle($myMarqueVehicleManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals($responseCode, $response->getStatusCode());
        $expectedResult = [
            'success' => [
                'vin' => 'VIN-234',
            ],
        ];
        $this->assertSame($expectedResult, json_decode($response->getContent(), true));
    }

    public function testAddVehicleWillReturnErrorResponse(): void
    {
        //$this->markTestSkipped('The api was disabled; will be removed in the future');
        $myMarqueVehicleManager = $this->createMock(MyMarqueVehicleManager::class);
        $myMarqueVehicleManager
            ->expects($this->once())
            ->method('addVehicleToUserGarage')
            ->willReturn(new ErrorResponse('Error response', Response::HTTP_BAD_REQUEST));

        $response = $this->callAddVehicle($myMarqueVehicleManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $expectedResult = [
            'error' => [
                'message' => 'Error response',
            ],
        ];
        $this->assertSame($expectedResult, json_decode($response->getContent(), true));
    }

    public function testAddVehicleWillThrowException(): void
    {
        //$this->markTestSkipped('The api was disabled; will be removed in the future');
        $myMarqueVehicleManager = $this->createMock(MyMarqueVehicleManager::class);
        $myMarqueVehicleManager
            ->expects($this->once())
            ->method('addVehicleToUserGarage')
            ->willThrowException(new \Exception('Error response', 456));

        $response = $this->callAddVehicle($myMarqueVehicleManager);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response->getStatusCode());
        $expectedResult = [
            'error' => [
                'message' => 'Error response',
            ],
        ];
        $this->assertSame($expectedResult, json_decode($response->getContent(), true));
    }
}

<?php

namespace App\Tests\Manager;

use App\Manager\VehicleManager;
use App\Service\UserDataService;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Document\Vehicle;
use Psr\Log\LoggerInterface;

class VehicleManagerODMTest extends TestCase
{
    private VehicleManager $vehicleManager;
    private MockObject|UserDataService $userDataService;
    private MockObject|LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->userDataService = $this->createMock(UserDataService::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        // Create all required dependencies as mocks
        $vehicleService = $this->createMock(\App\Service\VehicleService::class);
        $serializer = $this->createMock(\Symfony\Component\Serializer\SerializerInterface::class);
        $denormalizer = $this->createMock(\Symfony\Component\Serializer\Normalizer\DenormalizerInterface::class);
        $validator = $this->createMock(\Symfony\Component\Validator\Validator\ValidatorInterface::class);
        $dispatcher = $this->createMock(\Symfony\Component\EventDispatcher\EventDispatcherInterface::class);
        $xPVehicleRefreshService = $this->createMock(\App\Service\XPVehicleRefreshService::class);
        $xFVehicleRefreshService = $this->createMock(\App\Service\XFVehicleRefreshService::class);
        $corvetService = $this->createMock(\App\Service\CorvetService::class);
        $systemUserDataClient = $this->createMock(\App\Service\SystemUserDataClient::class);
        $visual3DManager = $this->createMock(\App\Manager\Visual3DManager::class);
        $brandHelper = $this->createMock(\App\Helper\BrandHelper::class);
        $systemSdprClient = $this->createMock(\App\Service\SystemSdprClient::class);
        $featureCodeService = $this->createMock(\App\Service\FeatureCodeService::class);
        $vehicleLabelService = $this->createMock(\App\Service\VehicleLabelService::class);
        $normalizer = $this->createMock(\Symfony\Component\Serializer\Normalizer\NormalizerInterface::class);
        $catalogManager = $this->createMock(\App\Manager\CatalogManager::class);
        $subscriptionManager = $this->createMock(\App\Manager\SubscriptionManager::class);
        $systemUserDBService = $this->createMock(\App\Service\SystemUserDBService::class);
        $systemUserDBManager = $this->createMock(\App\Manager\SystemUserDBManager::class);

        // Create the VehicleManager with all dependencies
        $this->vehicleManager = new VehicleManager(
            $vehicleService,
            $serializer,
            $denormalizer,
            $validator,
            $dispatcher,
            $xPVehicleRefreshService,
            $xFVehicleRefreshService,
            $this->userDataService,
            $corvetService,
            $systemUserDataClient,
            $visual3DManager,
            $brandHelper,
            $systemSdprClient,
            $featureCodeService,
            $vehicleLabelService,
            $normalizer,
            $catalogManager,
            $subscriptionManager,
            $systemUserDBService,
            $systemUserDBManager
        );

        // Set the logger using reflection
        $reflection = new \ReflectionClass($this->vehicleManager);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->vehicleManager, $this->logger);
    }

    public function testGetPsaIdUsesUserDataService(): void
    {
        $userId = 'test-user-123';
        $brand = 'PEUGEOT';
        $expectedPsaId = 'ACNT-PSA123456'; // Format expected by RefreshVehicleHelper::parsePsaId

        $this->userDataService
            ->expects($this->once())
            ->method('getPsaIdByUserAndBrand')
            ->with($userId, $brand)
            ->willReturn($expectedPsaId);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->vehicleManager);
        $method = $reflection->getMethod('getPsaId');
        $method->setAccessible(true);

        $result = $method->invoke($this->vehicleManager, $userId, $brand);

        // The method applies RefreshVehicleHelper::parsePsaId, so we expect the parsed result
        $this->assertEquals('PSA123456', $result);
    }

    public function testGetUserDbIdUsesUserDataService(): void
    {
        $userId = 'test-user-123';

        // Create a mock UserData object
        $userData = $this->createMock(UserData::class);
        $userData->method('getUserId')->willReturn($userId);

        $this->userDataService
            ->expects($this->once())
            ->method('findUserById')
            ->with($userId)
            ->willReturn($userData);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->vehicleManager);
        $method = $reflection->getMethod('getUserDbId');
        $method->setAccessible(true);

        $result = $method->invoke($this->vehicleManager, $userId);

        // Since extractUserDbIdFromODMUserData returns userId as userDbId
        $this->assertEquals($userId, $result);
    }

    public function testGetUserDbIdReturnsNullWhenUserNotFound(): void
    {
        $userId = 'non-existent-user';

        $this->userDataService
            ->expects($this->once())
            ->method('findUserById')
            ->with($userId)
            ->willReturn(null);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->vehicleManager);
        $method = $reflection->getMethod('getUserDbId');
        $method->setAccessible(true);

        $result = $method->invoke($this->vehicleManager, $userId);

        $this->assertNull($result);
    }

    public function testGetVehicleUsesUserDataService(): void
    {
        $userId = 'test-user-123';
        $vin = 'TEST123456789';

        $vehicle = new Vehicle();
        $vehicle->setVin($vin);
        $vehicle->setBrand('PEUGEOT');
        $vehicle->setStatus('SSDP');

        // Create a mock UserData object
        $userData = $this->createMock(UserData::class);
        $userData->method('getUserId')->willReturn($userId);
        $userData->method('getVehicles')->willReturn([$vehicle]);

        $this->userDataService
            ->expects($this->once())
            ->method('findUserById')
            ->with($userId)
            ->willReturn($userData);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->vehicleManager);
        $method = $reflection->getMethod('getVehicle');
        $method->setAccessible(true);

        $result = $method->invoke($this->vehicleManager, $userId, $vin, 'vin');

        $this->assertIsArray($result);
        $this->assertArrayHasKey('vehicle', $result);
        $this->assertArrayHasKey('userDbId', $result);
        $this->assertEquals($userId, $result['userDbId']); // userDbId is same as userId in ODM
        $this->assertCount(1, $result['vehicle']);
        $this->assertEquals($vehicle, $result['vehicle'][0]);
    }

    public function testGetVehicleReturnsNullWhenUserNotFound(): void
    {
        $userId = 'non-existent-user';
        $vin = 'TEST123456789';

        $this->userDataService
            ->expects($this->once())
            ->method('findUserById')
            ->with($userId)
            ->willReturn(null);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->vehicleManager);
        $method = $reflection->getMethod('getVehicle');
        $method->setAccessible(true);

        $result = $method->invoke($this->vehicleManager, $userId, $vin, 'vin');

        $this->assertNull($result);
    }

    public function testGetVehicleReturnsNullWhenVehicleNotFound(): void
    {
        $userId = 'test-user-123';
        $vin = 'NONEXISTENT123';

        $vehicle = new Vehicle();
        $vehicle->setVin('DIFFERENT123');
        $vehicle->setBrand('PEUGEOT');
        $vehicle->setStatus('SSDP');

        $userData = new UserData();
        $userData->setUserId($userId);
        $userData->addVehicle($vehicle);

        $this->userDataService
            ->expects($this->once())
            ->method('findUserById')
            ->with($userId)
            ->willReturn($userData);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->vehicleManager);
        $method = $reflection->getMethod('getVehicle');
        $method->setAccessible(true);

        $result = $method->invoke($this->vehicleManager, $userId, $vin, 'vin');

        $this->assertNull($result);
    }

    public function testGetVehicleFiltersByStatus(): void
    {
        $userId = 'test-user-123';
        $vin = 'TEST123456789';

        $vehicle = new Vehicle();
        $vehicle->setVin($vin);
        $vehicle->setBrand('PEUGEOT');
        $vehicle->setStatus('GSPD'); // Not SSDP

        $userData = new UserData();
        $userData->setUserId($userId);
        $userData->addVehicle($vehicle);

        $this->userDataService
            ->expects($this->once())
            ->method('findUserById')
            ->with($userId)
            ->willReturn($userData);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->vehicleManager);
        $method = $reflection->getMethod('getVehicle');
        $method->setAccessible(true);

        $result = $method->invoke($this->vehicleManager, $userId, $vin, 'vin');

        // Should return null because vehicle status is not SSDP
        $this->assertNull($result);
    }
}

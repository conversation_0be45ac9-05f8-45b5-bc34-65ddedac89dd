<?php

namespace App\Tests\Service;

use App\Service\UserDataService;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Space\MongoDocuments\Service\MongoDBService;
use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Document\Vehicle;
use Psr\Log\LoggerInterface;

class UserDataServiceTest extends TestCase
{
    private UserDataService $userDataService;
    private MockObject|MongoDBService $mongoDBService;
    private MockObject|LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->mongoDBService = $this->createMock(MongoDBService::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        
        $this->userDataService = new UserDataService($this->mongoDBService);
        
        // Use reflection to inject the logger
        $reflection = new \ReflectionClass($this->userDataService);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->userDataService, $this->logger);
    }

    public function testFindUserById(): void
    {
        $userId = 'test-user-123';
        $userData = new UserData();
        $userData->setUserId($userId);

        $this->mongoDBService
            ->expects($this->once())
            ->method('findOneBy')
            ->with(UserData::class, ['userId' => $userId])
            ->willReturn($userData);

        $result = $this->userDataService->findUserById($userId);

        $this->assertInstanceOf(UserData::class, $result);
        $this->assertEquals($userId, $result->getUserId());
    }

    public function testFindUserByIdNotFound(): void
    {
        $userId = 'non-existent-user';

        $this->mongoDBService
            ->expects($this->once())
            ->method('findOneBy')
            ->with(UserData::class, ['userId' => $userId])
            ->willReturn(null);

        $result = $this->userDataService->findUserById($userId);

        $this->assertNull($result);
    }

    public function testFindUserByIdWithException(): void
    {
        $userId = 'test-user-123';

        $this->mongoDBService
            ->expects($this->once())
            ->method('findOneBy')
            ->with(UserData::class, ['userId' => $userId])
            ->willThrowException(new \Exception('Database connection error'));

        $this->logger
            ->expects($this->once())
            ->method('error');

        // The method should throw a RuntimeException for MongoDB connection errors
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('MongoDB connection unavailable');

        $this->userDataService->findUserById($userId);
    }

    public function testAddVehicleToUserDocument(): void
    {
        $userId = 'test-user-123';
        $vehicle = new Vehicle();
        $vehicle->setVin('TEST123456789');
        $vehicle->setBrand('PEUGEOT');

        $userData = new UserData();
        $userData->setUserId($userId);

        $this->mongoDBService
            ->expects($this->once())
            ->method('findOneBy')
            ->with(UserData::class, ['userId' => $userId])
            ->willReturn($userData);

        $this->mongoDBService
            ->expects($this->once())
            ->method('save')
            ->with($userData);

        $result = $this->userDataService->addVehicleToUserDocument($userId, $vehicle);

        $this->assertTrue($result);
        $this->assertCount(1, $userData->getVehicles());
        $this->assertEquals('TEST123456789', $userData->getVehicles()[0]->getVin());
    }

    public function testAddVehicleToUserDocumentUserNotFound(): void
    {
        $userId = 'non-existent-user';
        $vehicle = new Vehicle();
        $vehicle->setVin('TEST123456789');

        $this->mongoDBService
            ->expects($this->once())
            ->method('findOneBy')
            ->with(UserData::class, ['userId' => $userId])
            ->willReturn(null);

        // The method creates a new UserData when user is not found, so save() will be called
        $this->mongoDBService
            ->expects($this->once())
            ->method('save')
            ->with($this->isInstanceOf(UserData::class));

        $result = $this->userDataService->addVehicleToUserDocument($userId, $vehicle);

        $this->assertTrue($result);
    }

    public function testConvertLegacyVehicleToODM(): void
    {
        $legacyVehicle = [
            'vin' => 'TEST123456789',
            'brand' => 'PEUGEOT',
            'shortLabel' => '308',
            'versionId' => 'VERSION123',
            'featureCode' => [
                ['code' => 'FC001', 'status' => 'enabled', 'value' => 'test']
            ],
            'sdp' => 'SSDP'
        ];

        $vehicle = $this->userDataService->convertLegacyVehicleToODM($legacyVehicle);

        $this->assertInstanceOf(Vehicle::class, $vehicle);
        $this->assertEquals('TEST123456789', $vehicle->getVin());
        $this->assertEquals('PEUGEOT', $vehicle->getBrand());
        $this->assertEquals('308', $vehicle->getModel());
        $this->assertEquals('VERSION123', $vehicle->getVersionId());
        $this->assertEquals('SSDP', $vehicle->getStatus());
        $this->assertIsArray($vehicle->getFeatureCode());
    }

    public function testFindUserVehicleByVin(): void
    {
        $userId = 'test-user-123';
        $vin = 'TEST123456789';
        
        $vehicle = new Vehicle();
        $vehicle->setVin($vin);
        $vehicle->setBrand('PEUGEOT');

        $userData = new UserData();
        $userData->setUserId($userId);
        $userData->addVehicle($vehicle);

        $this->mongoDBService
            ->expects($this->once())
            ->method('findOneBy')
            ->with(UserData::class, ['userId' => $userId])
            ->willReturn($userData);

        $result = $this->userDataService->findUserVehicleByVin($userId, $vin);

        $this->assertInstanceOf(Vehicle::class, $result);
        $this->assertEquals($vin, $result->getVin());
    }

    public function testUpdateVehicleInUserDocument(): void
    {
        $userId = 'test-user-123';
        $existingVehicle = new Vehicle();
        $existingVehicle->setVin('TEST123456789');
        $existingVehicle->setBrand('PEUGEOT');

        $updatedVehicle = new Vehicle();
        $updatedVehicle->setVin('TEST123456789');
        $updatedVehicle->setBrand('CITROEN');

        $userData = new UserData();
        $userData->setUserId($userId);
        $userData->addVehicle($existingVehicle);

        $this->mongoDBService
            ->expects($this->once())
            ->method('findOneBy')
            ->with(UserData::class, ['userId' => $userId])
            ->willReturn($userData);

        $this->mongoDBService
            ->expects($this->once())
            ->method('save')
            ->with($userData);

        $result = $this->userDataService->updateVehicleInUserDocument($userId, $updatedVehicle);

        $this->assertTrue($result);
        $this->assertEquals('CITROEN', $userData->getVehicles()[0]->getBrand());
    }

    public function testGetVehicleByUserIdAndBrand(): void
    {
        $userId = 'test-user-123';
        $brand = 'PEUGEOT';
        
        $vehicle1 = new Vehicle();
        $vehicle1->setVin('TEST123456789');
        $vehicle1->setBrand('PEUGEOT');

        $vehicle2 = new Vehicle();
        $vehicle2->setVin('TEST987654321');
        $vehicle2->setBrand('CITROEN');

        $userData = new UserData();
        $userData->setUserId($userId);
        $userData->addVehicle($vehicle1);
        $userData->addVehicle($vehicle2);

        $this->mongoDBService
            ->expects($this->once())
            ->method('findOneBy')
            ->with(UserData::class, ['userId' => $userId])
            ->willReturn($userData);

        $result = $this->userDataService->getVehicleByUserIdAndBrand($userId, $brand);

        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertEquals('PEUGEOT', $result[0]->getBrand());
    }

    public function testRemoveUserSSDPVehicles(): void
    {
        $userId = 'test-user-123';
        $vin = 'TEST123456789';
        
        $vehicle = new Vehicle();
        $vehicle->setVin($vin);
        $vehicle->setBrand('PEUGEOT');
        $vehicle->setStatus('SSDP');

        $userData = new UserData();
        $userData->setUserId($userId);
        $userData->addVehicle($vehicle);

        $this->mongoDBService
            ->expects($this->once())
            ->method('findOneBy')
            ->with(UserData::class, ['userId' => $userId])
            ->willReturn($userData);

        $this->mongoDBService
            ->expects($this->once())
            ->method('save')
            ->with($userData);

        $result = $this->userDataService->removeUserSSDPVehicles($userId, $vin);

        $this->assertTrue($result);
        $this->assertCount(0, $userData->getVehicles());
    }

    public function testGetPsaIdByUserAndBrand(): void
    {
        $userId = 'test-user-123';
        $brand = 'PEUGEOT';
        $expectedPsaId = 'PSA123456';

        $userData = new UserData();
        $userData->setUserId($userId);
        
        // Mock UserPsaId - we'll need to check the actual structure
        // For now, assume it has a method to get PSA ID by brand
        
        $this->mongoDBService
            ->expects($this->once())
            ->method('findOneBy')
            ->with(UserData::class, ['userId' => $userId])
            ->willReturn($userData);

        $result = $this->userDataService->getPsaIdByUserAndBrand($userId, $brand);

        // This test may need adjustment based on actual UserPsaId structure
        $this->assertNull($result); // Since we haven't set up PSA IDs in mock
    }
}

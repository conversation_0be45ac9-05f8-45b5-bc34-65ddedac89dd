<?php

require_once __DIR__ . '/vendor/autoload.php';

use MongoDB\Client;

// MongoDB connection (using preprod configuration)
$client = new Client("mongodb+srv://spacemwodm:<EMAIL>/?retryWrites=true&w=majority&ssl=true&tlsAllowInvalidCertificates=false");
$database = $client->selectDatabase('SpaceDb');
$collection = $database->selectCollection('userData');

// Find the user document
$userId = '411d31789b35409fb3b75a4d59cec234';
$vin = 'VYEATTEN0SPU00175';

$userDocument = $collection->findOne(['userId' => $userId]);

if ($userDocument) {
    echo "User document found!\n";
    echo "User ID: " . $userDocument['userId'] . "\n";
    echo "Document structure:\n";
    echo json_encode($userDocument, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "\n";
} else {
    echo "User document not found for userId: $userId\n";
}

<?php

namespace App\Manager;

use App\Helper\BrandHelper;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Serializer;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\MarketHelper;
use App\Helper\SuccessResponse;
use App\Service\FavoriteDealerService;
use App\Trait\LoggerTrait;

use App\Service\UserDataService;
use App\Service\SettingsService;
use App\Transformer\XpResponseTransformer;
use App\Transformer\XfEmeaResponseTransformer;
use App\Transformer\xfEmeaParameterRequestTransformer;

class FavoriteDealerManager
{
    use LoggerTrait;

    const COLLECTION = 'userData';
    const DEALER_UNKNOWN = "FO_Common_Error_Dealer_Unknown";

    private const COLLECTION_SETTINGS = 'settings';
    private Serializer $serializer;

    public function __construct(
        private ValidatorInterface $validator,
        private UserDataService $userDataService,
        private SettingsService $settingsService,
        private FavoriteDealerService $favoriteDealerService,
        private XpResponseTransformer $xpResponseTransformer
    ) {
        $this->serializer = new Serializer([new ObjectNormalizer()]);
    }

    public function saveFavoriteDealer(array $params): ResponseArrayFormat
    {
        $brand = $params['brand'];
        if (BrandHelper::isXP($brand)) {
            return $this->saveFavoriteDealerXP($params);
        }
        return $this->saveFavoriteDealerXF($params);
    }

    private function saveFavoriteDealerXP(array $params): ResponseArrayFormat
    {
        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Start saving XP favorite dealer in database', [
            'params' => $params,
        ]);

        $userId = $params['userId'];
        $brand = strtolower($params['brand']);
        $brandParameters = [
            'country' => $params['country'],
            'idSiteGeo' => $params['siteGeo'],
            'language' => $params['language']
        ];

        // Validate dealer exists before saving
        $response = $this->favoriteDealerService->getXpDealerDetails($params);
        if ($response->getCode() != 200) {
            return new ErrorResponse(self::DEALER_UNKNOWN, Response::HTTP_NOT_FOUND);
        }

        $response = $this->favoriteDealerService->setPreferredDealerForBrand($userId, $brand, $brandParameters);

        if ($response->getCode() === Response::HTTP_OK) {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' XP Favorite dealer is successfully added', [
                'userId' => $userId,
                'brand' => $brand,
                'dealerData' => $brandParameters,
            ]);
            return new SuccessResponse("Favourite dealer successfully added!", Response::HTTP_OK);
        }

        return $response;
    }

    private function saveFavoriteDealerXF(array $params): ResponseArrayFormat
    {
        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Start checking if vin exists for XF favorite dealer in database', [
            'params' => $params,
        ]);

        $siteGeo = $params['siteGeo'];
        $userId = $params['userId'];
        $vin = $params['vin'];

        // Set preferred dealer for the vehicle
        $brandParameters = [
            'country' => $params['country'],
            'idSiteGeo' => $siteGeo
        ];

        // Validate dealer exists before saving
        $response = $this->favoriteDealerService->getXfDealerDetails($params);
        if ($response->getCode() != 200) {
            return new ErrorResponse(self::DEALER_UNKNOWN, Response::HTTP_NOT_FOUND);
        }

        $response = $this->favoriteDealerService->setPreferredDealerForVehicle($userId, $vin, $brandParameters);

        if ($response->getCode() === Response::HTTP_OK) {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' XF Favorite dealer is successfully added', [
                'userId' => $userId,
                'vin' => $vin,
                'dealerData' => $brandParameters,
            ]);
            return new SuccessResponse("Favourite dealer successfully added!", Response::HTTP_OK);
        } elseif ($response->getCode() === Response::HTTP_NOT_FOUND) {
            return new ErrorResponse("The VIN is not part of the user's garage", Response::HTTP_FORBIDDEN);
        }

        return $response;
    }

    public function deleteFavoriteDealer(array $params)
    {
        $brand = $params['brand'];
        if (BrandHelper::isXP($brand)) {
            return $this->deleteFavoriteDealerXP($params);
        } elseif (BrandHelper::isXF($brand)) {
            return $this->deleteFavoriteDealerXF($params);
        }
    }

    private function deleteFavoriteDealerXP(array $params): ResponseArrayFormat
    {
        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Deleting XP favorite dealer from database', [
            'params' => $params,
        ]);

        $userId = $params['userId'];
        $brand = strtolower($params['brand']);

        $response = $this->favoriteDealerService->removePreferredDealerForBrand($userId, $brand);

        if ($response->getCode() === Response::HTTP_OK) {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' XP Favorite dealer is successfully deleted', [
                'userId' => $userId,
                'brand' => $brand,
            ]);
            return new SuccessResponse("successfully deleted!", Response::HTTP_OK);
        }

        return $response;
    }

    private function deleteFavoriteDealerXF(array $params): ResponseArrayFormat
    {
        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Start checking if vin exists for XF favorite dealer in database', [
            'params' => $params,
        ]);

        $userId = $params['userId'];
        $vin = $params['vin'];

        $response = $this->favoriteDealerService->removePreferredDealerForVehicle($userId, $vin);

        if ($response->getCode() === Response::HTTP_OK) {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' XF Favorite dealer is successfully deleted', [
                'userId' => $userId,
                'vin' => $vin,
            ]);
            return new SuccessResponse("successfully deleted!", Response::HTTP_OK);
        } elseif ($response->getCode() === Response::HTTP_NOT_FOUND) {
            return new ErrorResponse("The VIN is not part of the user's garage", Response::HTTP_FORBIDDEN);
        }

        return $response;
    }

    public function getFavoriteDealer(array $params): ResponseArrayFormat
    {
        $brand = $params['brand'];
        if (BrandHelper::isXP($brand)) {
            return $this->getFavoriteDealerXP($params);
        }
        return $this->getFavoriteDealerXF($params);
    }

    public function getFavoriteDealerXP(array $params): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting XP favorite dealer from database', [
                'params' => $params,
            ]);

            $userId = $params['userId'];
            $brand = strtolower($params['brand']);

            // Get preferred dealer for the brand using service layer
            $site_geo = $this->favoriteDealerService->getPreferredDealerForBrand($userId, $brand);
            if ($site_geo === null) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' No preferred dealer found for brand', [
                    'userId' => $userId,
                    'brand' => $brand,
                ]);
                return new ErrorResponse("No favorite dealer defined for the user", Response::HTTP_FORBIDDEN);
            }

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' XP Favorite dealer is successfully fetched', [
                'userId' => $userId,
                'brand' => $brand,
                'dealerData' => $site_geo,
            ]);

            $params['country'] = $site_geo['country'] ?? '';
            $params['language'] = $site_geo['language'] ?? '';
            $params['siteGeo'] = $site_geo['idSiteGeo'] ?? '';

            $response = $this->favoriteDealerService->getXpDealerDetails($params);

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Favorite dealer is successfully fetched', [
                'response' => $response->getData(),
            ]);
            if (Response::HTTP_OK == $response->getCode()) {
                $data = $response->getData()['success'] ?? [];
                if (empty($data)) {
                    return new ErrorResponse("No favorite dealer defined for the user", Response::HTTP_FORBIDDEN);
                }
                $params['o2x'] = $this->getO2xSettings($params['brand'], $params['source']);
                $response = $this->xpResponseTransformer->mapper($data, $params);
                $dealerResponse = ["Dealer" => $response];
                return new SuccessResponse($dealerResponse, Response::HTTP_OK);
            }
            $responseData = $response->getData();
            $result = (isset($responseData['error']['message'])) ? $responseData['error']['message'] : $responseData;
            return new ErrorResponse($result, code: $response->getCode());

        } catch (\RuntimeException $e) {
            if ($e->getCode() === 503) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' MongoDB connection unavailable', [
                    'userId' => $params['userId'],
                    'brand' => $params['brand'],
                ]);
                return new ErrorResponse("Database service temporarily unavailable", Response::HTTP_SERVICE_UNAVAILABLE);
            }
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting favorite dealer: ' . $e->getMessage(), [
                'params' => $params,
                'exception' => $e,
            ]);
            return new ErrorResponse("Error getting favorite dealer", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getFavoriteDealerXF(array $params): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Starting XF favorite dealer retrieval', [
                'params' => $params,
            ]);

            $userId = $params['userId'];
            $vehicleId = $params['vin'];

            // Get preferred dealer for the vehicle using service layer
            try {
                $preferredDealer = $this->favoriteDealerService->getPreferredDealerForVehicle($userId, $vehicleId);
            } catch (\RuntimeException $e) {
                if ($e->getCode() === 503) {
                    $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' MongoDB connection unavailable', [
                        'userId' => $userId,
                        'vin' => $vehicleId,
                    ]);
                    return new ErrorResponse("Database service temporarily unavailable", Response::HTTP_SERVICE_UNAVAILABLE);
                }
                throw $e;
            }

            if (!$preferredDealer || empty($preferredDealer['idSiteGeo'])) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' No preferred dealer found for vehicle', [
                    'vin' => $vehicleId,
                    'userId' => $userId,
                    'preferredDealer' => $preferredDealer,
                ]);
                return new ErrorResponse("No favorite dealer defined for the user", Response::HTTP_FORBIDDEN);
            }

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Preferred dealer found for vehicle', [
                'vin' => $vehicleId,
                'userId' => $userId,
                'preferredDealer' => $preferredDealer,
            ]);

            $params['country'] = $preferredDealer['country'] ?? "";
            $params['siteGeo'] = $preferredDealer['idSiteGeo'] ?? "";

            $response = $this->favoriteDealerService->getXfDealerDetails($params);

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' XF favorite dealer fetched successfully', [
                'params' => $params,
                'response_code' => $response->getCode(),
            ]);

            if ($response->getCode() !== 200) {
                $responseData = $response->getData();
                $result = (isset($responseData['error']['message'])) ? $responseData['error']['message'] : $responseData;
                return new ErrorResponse($result, code: $response->getCode());
            }

            $data = $response->getData()['success'] ?? [];
            if (empty($data)) {
                return new ErrorResponse('No favorite dealer defined for the user', Response::HTTP_FORBIDDEN);
            }

            $marketCode = MarketHelper::getMarket($params['country']);
            $brandCode = BrandHelper::getBrandCode($params['brand']);
            $params = xfEmeaParameterRequestTransformer::mapper(
                $params,
                $marketCode,
                $brandCode
            );
            $xfEmeaResponse = XfEmeaResponseTransformer::mapper(
                $data ?? [],
                $params
            );

            return new SuccessResponse($xfEmeaResponse->getSuccess(), Response::HTTP_OK);

        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting favorite dealer: ' . $e->getMessage(), [
                'params' => $params,
                'exception' => $e,
            ]);
            return new ErrorResponse("Error getting favorite dealer", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    private function getO2xSettings(string $brand, $source = "APP"): array
    {
        return $this->settingsService->getO2xSettings($brand, $source);
    }
}

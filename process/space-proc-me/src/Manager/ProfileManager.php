<?php
namespace App\Manager;

use Exception;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Service\ProfileService;
use App\Model\ProfileModel;
use App\Manager\UserManager;
use Symfony\Component\HttpFoundation\Response;
use App\Trait\LoggerTrait;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use App\Helper\ResponseArrayFormat;

/**
 * Manager of the user Account
 */
class ProfileManager {
    use LoggerTrait;

    /**
     * @param ValidatorInterface $validator
     * @param ProfileService $profileService
     */
    public function __construct(private ValidatorInterface $validator,
        private ProfileService $profileService,
        private UserManager $userManager)
    {}

    /**
     * Description: update profile user account
     * @param string $id
     * @param array $data
     * @return array
     */
    public function putUserData(string $id, ?array $data): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . "::" . __METHOD__ . " Put User data");
            $userData = $this->userManager->getUserByUserId($id);
            if(empty($userData)) {
                return new ErrorResponse("customer not found", Response::HTTP_NOT_FOUND);
            }
            // Prepare the data for the update
            $toUpdate = [];

            // Map the fields to the format expected by the service - only if they exist in data
            if (isset($data["first_name"])) {
                $toUpdate["firstName"] = $data["first_name"];
            }
            if (isset($data["last_name"])) {
                $toUpdate["lastName"] = $data["last_name"];
            }
            if (isset($data["civility"])) {
                $toUpdate["civility"] = $data["civility"];
            }
            if (isset($data["zip_code"])) {
                $toUpdate["postCode"] = $data["zip_code"];
            }
            if (isset($data["address1"])) {
                $toUpdate["geopoint"] = $data["address1"];
            }
            if (isset($data["city"])) {
                $toUpdate["town"] = $data["city"];
            }
            if (isset($data["country"])) {
                $toUpdate["countryCode"] = $data["country"];
            }
            if (isset($data["phone"])) {
                $toUpdate["phoneNumber"] = $data["phone"];
            }
            if (isset($data["email"])) {
                $toUpdate["primaryEmail"] = $data["email"];
            }
            if (isset($data["address2"])) {
                $toUpdate["street"] = $data["address2"];
            }

            $response = $this->profileService->putUserData($userData["userDbId"], $toUpdate);

            if (Response::HTTP_OK == $response->getCode()) {
                $respData = $this->mappingUserDataResponse($id, $response->getData()['success']);
                // Use the converted data for updating the user
                $this->userManager->updateUserByUserDbId($userData["userDbId"], $toUpdate);
                return new SuccessResponse($respData, $response->getCode());
            }
            $this->logger->error("Error: Put Profile data for (" . json_encode($response) . ")");
            $message = $response->getData() ?? 'ErrorResponse in getting profile customer: '.$id;
            $code = $response->getCode() ?? Response::HTTP_INTERNAL_SERVER_ERROR;
            return new ErrorResponse($message, $code);
        } catch (Exception $e) {
            $this->logger->error("Error: Put Profile data for $id ".$e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Description: get profile user account
     * @param string $id
     * @return array
     */
    public function getUserData(string $id): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . "::" . __METHOD__ . " get User data");
            $userData = $this->userManager->getUserByUserId($id);
            if(empty($userData)) {
                return new ErrorResponse("customer not found", Response::HTTP_NOT_FOUND);
            }

            $response = $this->profileService->getUserData($userData["userDbId"]);

            if (Response::HTTP_OK == $response->getCode()) {
                $data = $this->mappingUserDataResponse($id, $response->getData()['success']);
                #invalidate cache related to profile
                return new SuccessResponse($data, $response->getCode());
            }
            $this->logger->error("Error: get Profile data for $id (" . json_encode($response) . ")");
            $message = $response->getData() ?? 'ErrorResponse in getting profile customer: '.$id;
            $code = $response->getCode() ?? Response::HTTP_INTERNAL_SERVER_ERROR;
            return new ErrorResponse($message, $code);
        } catch (Exception $e) {
            $this->logger->error("Error: get Profile data for $id ".$e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    private function mappingUserDataResponse(string $id, ?array $data) {
        return [
            'id' => $id,
            'first_name' => $data["firstName"] ?? "",
            'last_name' => $data["lastName"] ?? "",
            'civility' => $data["civility"] ?? "",
            'civility_code' => "",
            'address1' => $data["geopoint"] ?? "",
            'city' => $data["town"] ?? "",
            'country' => $data["countryCode"] ?? "",
            'zip_code' => $data["postCode"] ?? "",
            'phone' => $data["phoneNumber"] ?? "",
            'address2' => $data["street"] ?? "",
            'email' => $data["email"] ?? ($data["primaryEmail"] ?? "")
        ];
    }
}